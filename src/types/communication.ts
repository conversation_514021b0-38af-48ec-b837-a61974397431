export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderOrganization: string;
  recipientId: string;
  recipientName: string;
  recipientOrganization: string;
  content: string;
  timestamp: string;
  isRead: boolean;
  messageType: 'text' | 'system' | 'file';
  attachments?: MessageAttachment[];
  relatedJobId?: string;
  relatedCandidateId?: string;
}

export interface MessageAttachment {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  url: string;
}

export interface Conversation {
  id: string;
  participants: ConversationParticipant[];
  lastMessage?: Message;
  lastActivity: string;
  isActive: boolean;
  subject?: string;
  relatedJobId?: string;
  relatedCandidateId?: string;
  unreadCount: number;
}

export interface ConversationParticipant {
  userId: string;
  userName: string;
  organizationId: string;
  organizationName: string;
  role: 'client' | 'vendor';
  avatar?: string;
  isOnline: boolean;
  lastSeen?: string;
}

export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  actionUrl?: string;
  relatedJobId?: string;
  relatedCandidateId?: string;
  relatedUserId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'job' | 'candidate' | 'vendor' | 'system' | 'message';
}

export type NotificationType = 
  | 'job_assigned'
  | 'candidate_submitted'
  | 'candidate_approved'
  | 'candidate_rejected'
  | 'interview_scheduled'
  | 'message_received'
  | 'vendor_invitation'
  | 'vendor_approved'
  | 'deadline_reminder'
  | 'system_update';

export interface ActivityFeedItem {
  id: string;
  userId: string;
  type: ActivityType;
  title: string;
  description: string;
  timestamp: string;
  actorId: string;
  actorName: string;
  actorOrganization: string;
  targetId?: string;
  targetName?: string;
  relatedJobId?: string;
  relatedCandidateId?: string;
  icon: string;
  color: string;
}

export type ActivityType = 
  | 'job_created'
  | 'job_assigned'
  | 'candidate_submitted'
  | 'candidate_approved'
  | 'candidate_rejected'
  | 'interview_scheduled'
  | 'vendor_connected'
  | 'message_sent'
  | 'profile_updated';

export interface OnlineStatus {
  userId: string;
  isOnline: boolean;
  lastSeen: string;
  status: 'online' | 'away' | 'busy' | 'offline';
}

export interface MessageThread {
  conversationId: string;
  messages: Message[];
  participants: ConversationParticipant[];
  isLoading: boolean;
  hasMore: boolean;
}

export interface NotificationSettings {
  userId: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  jobAssignments: boolean;
  candidateSubmissions: boolean;
  candidateUpdates: boolean;
  messages: boolean;
  vendorUpdates: boolean;
  systemUpdates: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}
