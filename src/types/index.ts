export interface Candidate {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  applicationDate: string;
  status: 'applied' | 'screening' | 'interview' | 'offer' | 'hired' | 'rejected' | 'onboarding';
  resume?: string;
  coverLetter?: string;
  experience: number;
  skills: string[];
  interviewDate?: string;
  interviewFeedback?: string;
  onboardingStage?: 'documentation' | 'training' | 'setup' | 'completed';
  startDate?: string;
  avatar?: string;
}

export interface Job {
  id: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  description: string;
  requirements: string[];
  postedDate: string;
  isActive: boolean;
}

export interface OnboardingTask {
  id: string;
  candidateId: string;
  title: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'in-progress' | 'completed';
  assignedTo: string;
  category: 'documentation' | 'training' | 'setup' | 'orientation';
}

export interface Interview {
  id: string;
  candidateId: string;
  date: string;
  time: string;
  interviewer: string;
  type: 'phone' | 'video' | 'in-person';
  status: 'scheduled' | 'completed' | 'cancelled';
  feedback?: string;
  rating?: number;
}