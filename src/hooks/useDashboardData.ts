import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { mockCandidates, mockJobs, mockInterviews } from '@/data/mockData';
import { mockOrganizations, mockVendorClientRelationships } from '@/data/multiTenantData';

// Types for dashboard statistics
export interface DashboardStat {
  name: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
}

export interface CandidateData {
  id: string;
  name: string;
  position: string;
  status: string;
  avatar?: string;
  submittedAt: string;
}

export interface JobData {
  id: string;
  title: string;
  department: string;
  applicants: number;
  status: string;
  priority: 'high' | 'medium' | 'low';
}

// Custom hook for client dashboard data
export const useClientDashboardData = () => {
  const { organization } = useAuth();

  return useMemo(() => {
    if (!organization) return null;

    // Get connected vendors
    const connectedVendors = mockOrganizations.filter(org =>
      org.type === 'vendor' &&
      mockVendorClientRelationships.some(rel =>
        rel.clientId === organization.id && rel.vendorId === org.id && rel.status === 'approved'
      )
    );

    // Calculate client-specific statistics
    const activeJobs = mockJobs.filter(job => job.isActive).length;
    const totalCandidates = mockCandidates.length;
    const scheduledInterviews = mockInterviews.filter(interview => interview.status === 'scheduled').length;
    const pendingReviews = mockCandidates.filter(c => c.status === 'review').length;
    const thisMonthHires = mockCandidates.filter(c => c.status === 'hired').length;

    // Recent candidates for client dashboard
    const recentCandidates: CandidateData[] = mockCandidates.slice(0, 4).map(candidate => ({
      id: candidate.id,
      name: `${candidate.firstName} ${candidate.lastName}`,
      position: candidate.desiredPosition || 'Software Engineer',
      status: candidate.status,
      avatar: candidate.avatar,
      submittedAt: candidate.submittedAt || new Date().toISOString(),
    }));

    // Onboarding candidates
    const onboardingCandidates: CandidateData[] = mockCandidates
      .filter(c => c.status === 'onboarding')
      .map(candidate => ({
        id: candidate.id,
        name: `${candidate.firstName} ${candidate.lastName}`,
        position: candidate.desiredPosition || 'Software Engineer',
        status: candidate.status,
        avatar: candidate.avatar,
        submittedAt: candidate.submittedAt || new Date().toISOString(),
      }));

    return {
      organization,
      connectedVendors,
      stats: {
        activeJobs,
        totalCandidates,
        scheduledInterviews,
        pendingReviews,
        thisMonthHires,
        connectedVendorsCount: connectedVendors.length,
      },
      recentCandidates,
      onboardingCandidates,
    };
  }, [organization]);
};

// Custom hook for vendor dashboard data
export const useVendorDashboardData = () => {
  const { organization } = useAuth();

  return useMemo(() => {
    if (!organization) return null;

    // Get client relationships
    const clientRelationships = mockVendorClientRelationships.filter(
      rel => rel.vendorId === organization.id && rel.status === 'approved'
    );

    const connectedClients = mockOrganizations.filter(org =>
      org.type === 'client' &&
      clientRelationships.some(rel => rel.clientId === org.id)
    );

    // Mock vendor-specific calculations (in real app, these would come from API)
    const assignedJobs = Math.floor(Math.random() * 8) + 3;
    const submittedCandidates = Math.floor(Math.random() * 20) + 8;
    const approvedCandidates = Math.floor(Math.random() * 12) + 4;
    const clientRating = 4.2 + Math.random() * 0.6;

    // Recent submissions for vendor
    const recentSubmissions: CandidateData[] = mockCandidates.slice(0, 5).map(candidate => ({
      id: candidate.id,
      name: `${candidate.firstName} ${candidate.lastName}`,
      position: candidate.desiredPosition || 'Software Engineer',
      status: candidate.status,
      avatar: candidate.avatar,
      submittedAt: candidate.submittedAt || new Date().toISOString(),
    }));

    return {
      organization,
      connectedClients,
      stats: {
        assignedJobs,
        submittedCandidates,
        approvedCandidates,
        clientRating: clientRating.toFixed(1),
        connectedClientsCount: connectedClients.length,
        activeSubmissions: recentSubmissions.filter(s => ['review', 'interview'].includes(s.status)).length,
      },
      recentSubmissions,
    };
  }, [organization]);
};

// Custom hook for general dashboard statistics
export const useGeneralDashboardData = () => {
  return useMemo(() => {
    const totalCandidates = mockCandidates.length;
    const activeJobs = mockJobs.filter(job => job.isActive).length;
    const scheduledInterviews = mockInterviews.filter(interview => interview.status === 'scheduled').length;
    const hireRate = '78%'; // Mock calculation

    // Recent candidates
    const recentCandidates: CandidateData[] = mockCandidates.slice(0, 5).map(candidate => ({
      id: candidate.id,
      name: `${candidate.firstName} ${candidate.lastName}`,
      position: candidate.desiredPosition || 'Software Engineer',
      status: candidate.status,
      avatar: candidate.avatar,
      submittedAt: candidate.submittedAt || new Date().toISOString(),
    }));

    // Onboarding progress
    const onboardingCandidates: CandidateData[] = mockCandidates
      .filter(c => c.status === 'onboarding')
      .map(candidate => ({
        id: candidate.id,
        name: `${candidate.firstName} ${candidate.lastName}`,
        position: candidate.desiredPosition || 'Software Engineer',
        status: candidate.status,
        avatar: candidate.avatar,
        submittedAt: candidate.submittedAt || new Date().toISOString(),
      }));

    return {
      stats: {
        totalCandidates,
        activeJobs,
        scheduledInterviews,
        hireRate,
      },
      recentCandidates,
      onboardingCandidates,
    };
  }, []);
};

// Utility function to get status color
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'hired':
      return 'bg-green-100 text-green-800';
    case 'interview':
    case 'scheduled':
      return 'bg-blue-100 text-blue-800';
    case 'review':
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'onboarding':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Utility function to get priority color
export const getPriorityColor = (priority: string): string => {
  switch (priority.toLowerCase()) {
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};
