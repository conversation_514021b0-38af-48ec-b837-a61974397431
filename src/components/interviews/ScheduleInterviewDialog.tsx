import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { mockCandidates } from '@/data/mockData';
import { format } from 'date-fns';
import { CalendarIcon, Plus, X } from 'lucide-react';

interface ScheduleInterviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  newInterview: {
    candidateId: string;
    type: string;
    date: string;
    time: string;
    duration: string;
    location: string;
    notes: string;
    interviewers: string[];
    meetingLink: string;
    agenda: string;
  };
  onInterviewChange: (field: string, value: string | string[]) => void;
  onSchedule: () => void;
}

export default function ScheduleInterviewDialog({
  isOpen,
  onClose,
  newInterview,
  onInterviewChange,
  onSchedule
}: ScheduleInterviewDialogProps) {
  const selectedDate = newInterview.date ? new Date(newInterview.date) : undefined;

  const addInterviewer = () => {
    onInterviewChange('interviewers', [...newInterview.interviewers, '']);
  };

  const updateInterviewer = (index: number, value: string) => {
    const updated = [...newInterview.interviewers];
    updated[index] = value;
    onInterviewChange('interviewers', updated);
  };

  const removeInterviewer = (index: number) => {
    const updated = newInterview.interviewers.filter((_, i) => i !== index);
    onInterviewChange('interviewers', updated);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Schedule New Interview</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="candidate">Candidate</Label>
                <Select value={newInterview.candidateId} onValueChange={(value) => onInterviewChange('candidateId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select candidate" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockCandidates.map((candidate) => (
                      <SelectItem key={candidate.id} value={candidate.id}>
                        {candidate.firstName} {candidate.lastName} - {candidate.position}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="type">Interview Type</Label>
                <Select value={newInterview.type} onValueChange={(value) => onInterviewChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="video">Video Call</SelectItem>
                    <SelectItem value="phone">Phone Call</SelectItem>
                    <SelectItem value="in-person">In-Person</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {selectedDate ? format(selectedDate, 'PPP') : 'Pick a date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={(date) => onInterviewChange('date', date ? format(date, 'yyyy-MM-dd') : '')}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div>
                  <Label htmlFor="time">Time</Label>
                  <Input
                    id="time"
                    type="time"
                    value={newInterview.time}
                    onChange={(e) => onInterviewChange('time', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="duration">Duration (minutes)</Label>
                <Select value={newInterview.duration} onValueChange={(value) => onInterviewChange('duration', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="45">45 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                    <SelectItem value="90">1.5 hours</SelectItem>
                    <SelectItem value="120">2 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {newInterview.type === 'video' && (
                <div>
                  <Label htmlFor="meetingLink">Meeting Link</Label>
                  <Input
                    id="meetingLink"
                    placeholder="https://zoom.us/j/..."
                    value={newInterview.meetingLink}
                    onChange={(e) => onInterviewChange('meetingLink', e.target.value)}
                  />
                </div>
              )}

              {newInterview.type === 'in-person' && (
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    placeholder="Conference Room A, 123 Main St"
                    value={newInterview.location}
                    onChange={(e) => onInterviewChange('location', e.target.value)}
                  />
                </div>
              )}

              <div>
                <Label>Interviewers</Label>
                <div className="space-y-2">
                  {newInterview.interviewers.map((interviewer, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Input
                        placeholder="Interviewer name"
                        value={interviewer}
                        onChange={(e) => updateInterviewer(index, e.target.value)}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeInterviewer(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addInterviewer}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Interviewer
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="agenda">Interview Agenda</Label>
                <Textarea
                  id="agenda"
                  placeholder="Technical discussion, cultural fit, Q&A..."
                  value={newInterview.agenda}
                  onChange={(e) => onInterviewChange('agenda', e.target.value)}
                  rows={3}
                />
              </div>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              placeholder="Any special instructions or requirements..."
              value={newInterview.notes}
              onChange={(e) => onInterviewChange('notes', e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={onSchedule}>
              <CalendarIcon className="mr-2 h-4 w-4" />
              Schedule Interview
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
