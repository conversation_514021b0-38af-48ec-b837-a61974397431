import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Interview } from '@/types';
import { CalendarIcon, CheckCircle } from 'lucide-react';
import InterviewCard from './InterviewCard';

interface InterviewListViewProps {
  upcomingInterviews: Interview[];
  completedInterviews: Interview[];
  isClient: boolean;
  onProvideFeedback?: (interview: Interview) => void;
  onReschedule?: (interview: Interview) => void;
  onViewDetails?: (interview: Interview) => void;
}

export default function InterviewListView({ 
  upcomingInterviews,
  completedInterviews,
  isClient,
  onProvideFeedback,
  onReschedule,
  onViewDetails
}: InterviewListViewProps) {
  return (
    <div className="space-y-6">
      {/* Upcoming Interviews */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CalendarIcon className="mr-2 h-5 w-5" />
            Upcoming Interviews ({upcomingInterviews.length})
          </CardT<PERSON>le>
        </CardHeader>
        <CardContent>
          {upcomingInterviews.length > 0 ? (
            <div className="space-y-4">
              {upcomingInterviews.map((interview) => (
                <InterviewCard
                  key={interview.id}
                  interview={interview}
                  isClient={isClient}
                  onProvideFeedback={onProvideFeedback}
                  onReschedule={onReschedule}
                  onViewDetails={onViewDetails}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <p>No upcoming interviews scheduled</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Completed Interviews */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="mr-2 h-5 w-5" />
            Completed Interviews ({completedInterviews.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {completedInterviews.length > 0 ? (
            <div className="space-y-4">
              {completedInterviews.map((interview) => (
                <div key={interview.id} className="border rounded-lg p-4 bg-gray-50">
                  <InterviewCard
                    interview={interview}
                    isClient={isClient}
                    onProvideFeedback={onProvideFeedback}
                    onReschedule={onReschedule}
                    onViewDetails={onViewDetails}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <p>No completed interviews yet</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
