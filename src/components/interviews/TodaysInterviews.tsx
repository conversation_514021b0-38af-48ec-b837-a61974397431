import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Interview } from '@/types';
import { Clock } from 'lucide-react';
import InterviewCard from './InterviewCard';

interface TodaysInterviewsProps {
  interviews: Interview[];
  isClient: boolean;
  onProvideFeedback?: (interview: Interview) => void;
  onReschedule?: (interview: Interview) => void;
  onViewDetails?: (interview: Interview) => void;
}

export default function TodaysInterviews({ 
  interviews, 
  isClient,
  onProvideFeedback,
  onReschedule,
  onViewDetails
}: TodaysInterviewsProps) {
  if (interviews.length === 0) {
    return null;
  }

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center text-blue-900">
          <Clock className="mr-2 h-5 w-5" />
          Today's Interviews ({interviews.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {interviews.map((interview) => (
            <InterviewCard
              key={interview.id}
              interview={interview}
              isClient={isClient}
              variant="compact"
              showActions={false}
              onProvideFeedback={onProvideFeedback}
              onReschedule={onReschedule}
              onViewDetails={onViewDetails}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
