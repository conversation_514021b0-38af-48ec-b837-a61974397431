import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Interview } from '@/types';
import { Clock } from 'lucide-react';
import InterviewCard from './InterviewCard';

interface InterviewTimelineViewProps {
  interviews: Interview[];
  isClient: boolean;
  onProvideFeedback?: (interview: Interview) => void;
  onReschedule?: (interview: Interview) => void;
  onViewDetails?: (interview: Interview) => void;
}

export default function InterviewTimelineView({ 
  interviews,
  isClient,
  onProvideFeedback,
  onReschedule,
  onViewDetails
}: InterviewTimelineViewProps) {
  // Sort interviews by date and time
  const sortedInterviews = [...interviews]
    .sort((a, b) => {
      const dateA = new Date(`${a.date}T${a.time}`);
      const dateB = new Date(`${b.date}T${b.time}`);
      return dateB.getTime() - dateA.getTime(); // Most recent first
    })
    .slice(0, 10); // Show only the 10 most recent

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="mr-2 h-5 w-5" />
          Interview Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {sortedInterviews.map((interview, index) => (
            <div key={interview.id} className="relative">
              <InterviewCard
                interview={interview}
                isClient={isClient}
                variant="timeline"
                showActions={false}
                onProvideFeedback={onProvideFeedback}
                onReschedule={onReschedule}
                onViewDetails={onViewDetails}
              />
              {/* Remove the connecting line for the last item */}
              {index === sortedInterviews.length - 1 && (
                <div className="absolute left-[5px] top-[60px] w-px h-4 bg-white"></div>
              )}
            </div>
          ))}
          {interviews.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Clock className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <p>No interviews in timeline</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
