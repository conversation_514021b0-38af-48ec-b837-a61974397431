import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { mockCandidates } from '@/data/mockData';
import { Interview } from '@/types';
import { format, parseISO } from 'date-fns';
import {
  Clock,
  Video,
  Phone,
  MapPin,
  Star,
  MessageSquare,
  CheckCircle,
  XCircle,
  AlertCircle,
  Copy,
  ExternalLink,
  FileText,
  Mail
} from 'lucide-react';

interface InterviewCardProps {
  interview: Interview;
  isClient: boolean;
  onProvideFeedback?: (interview: Interview) => void;
  onReschedule?: (interview: Interview) => void;
  onViewDetails?: (interview: Interview) => void;
  showActions?: boolean;
  variant?: 'default' | 'compact' | 'timeline';
}

const getInterviewTypeIcon = (type: string) => {
  switch (type) {
    case 'video':
      return Video;
    case 'phone':
      return Phone;
    case 'in-person':
      return MapPin;
    default:
      return Video;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'scheduled':
      return 'bg-blue-100 text-blue-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatInterviewTime = (interview: Interview) => {
  try {
    const dateTime = parseISO(`${interview.date}T${interview.time}`);
    return format(dateTime, 'MMM d, h:mm a');
  } catch {
    return `${interview.date} ${interview.time}`;
  }
};

export default function InterviewCard({ 
  interview, 
  isClient, 
  onProvideFeedback,
  onReschedule,
  onViewDetails,
  showActions = true,
  variant = 'default'
}: InterviewCardProps) {
  const candidate = mockCandidates.find(c => c.id === interview.candidateId);
  const IconComponent = getInterviewTypeIcon(interview.type);

  if (!candidate) return null;

  if (variant === 'compact') {
    return (
      <div className="flex items-center justify-between p-2 bg-white rounded border">
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={candidate.avatar} />
            <AvatarFallback>
              {candidate.firstName[0]}{candidate.lastName[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-sm">{candidate.firstName} {candidate.lastName}</p>
            <p className="text-xs text-gray-600">{formatInterviewTime(interview)}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getStatusColor(interview.status)}>
            {interview.status}
          </Badge>
          <IconComponent className="h-4 w-4 text-gray-400" />
        </div>
      </div>
    );
  }

  if (variant === 'timeline') {
    return (
      <div className="flex items-start space-x-4">
        <div className="flex flex-col items-center">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <div className="w-px h-16 bg-gray-200 mt-2"></div>
        </div>
        <div className="flex-1 pb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={candidate.avatar} />
                <AvatarFallback>
                  {candidate.firstName[0]}{candidate.lastName[0]}
                </AvatarFallback>
              </Avatar>
              <div>
                <h4 className="font-medium">{candidate.firstName} {candidate.lastName}</h4>
                <p className="text-sm text-gray-600">{candidate.position}</p>
              </div>
            </div>
            <Badge className={getStatusColor(interview.status)}>
              {interview.status}
            </Badge>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            <div className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              {formatInterviewTime(interview)}
            </div>
            <div className="flex items-center mt-1">
              <IconComponent className="mr-2 h-4 w-4" />
              {interview.type === 'video' ? 'Video Call' : 
               interview.type === 'phone' ? 'Phone Call' : 'In-Person'}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-4">
          <Avatar className="h-12 w-12">
            <AvatarImage src={candidate.avatar} />
            <AvatarFallback>
              {candidate.firstName[0]}{candidate.lastName[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-lg">{candidate.firstName} {candidate.lastName}</h3>
            <p className="text-gray-600">{candidate.position}</p>
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
              <div className="flex items-center">
                <Clock className="mr-1 h-4 w-4" />
                {formatInterviewTime(interview)} (60min)
              </div>
              <div className="flex items-center">
                <IconComponent className="mr-1 h-4 w-4" />
                {interview.type === 'video' ? 'Video Call' : 
                 interview.type === 'phone' ? 'Phone Call' : 'In-Person'}
              </div>
              <div className="flex items-center">
                <MessageSquare className="mr-1 h-4 w-4" />
                {interview.interviewer}
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getStatusColor(interview.status)}>
            {interview.status}
          </Badge>
        </div>
      </div>

      {showActions && (
        <div className="flex items-center justify-between mt-4 pt-4 border-t">
          <div className="flex items-center space-x-2">
            {interview.type === 'video' && (
              <Button size="sm" variant="outline">
                <Video className="mr-1 h-3 w-3" />
                Join Call
              </Button>
            )}
            <Button size="sm" variant="outline">
              <Copy className="mr-1 h-3 w-3" />
              Copy Link
            </Button>
            <Button size="sm" variant="outline">
              <Mail className="mr-1 h-3 w-3" />
              Send Reminder
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            {interview.status === 'completed' && isClient && onProvideFeedback && (
              <Button size="sm" onClick={() => onProvideFeedback(interview)}>
                <FileText className="mr-1 h-3 w-3" />
                Feedback
              </Button>
            )}
            {interview.status === 'scheduled' && onReschedule && (
              <Button size="sm" variant="outline" onClick={() => onReschedule(interview)}>
                <Clock className="mr-1 h-3 w-3" />
                Reschedule
              </Button>
            )}
            {onViewDetails && (
              <Button size="sm" variant="outline" onClick={() => onViewDetails(interview)}>
                <ExternalLink className="mr-1 h-3 w-3" />
                Details
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
