import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { mockCandidates } from '@/data/mockData';
import { Interview } from '@/types';
import { format, parseISO } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

interface RescheduleInterviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedInterview: Interview | null;
  rescheduleData: {
    newDate: string;
    newTime: string;
    reason: string;
  };
  onRescheduleChange: (field: string, value: string) => void;
  onReschedule: () => void;
}

const formatInterviewTime = (interview: Interview) => {
  try {
    const dateTime = parseISO(`${interview.date}T${interview.time}`);
    return format(dateTime, 'MMM d, yyyy h:mm a');
  } catch {
    return `${interview.date} ${interview.time}`;
  }
};

export default function RescheduleInterviewDialog({
  isOpen,
  onClose,
  selectedInterview,
  rescheduleData,
  onRescheduleChange,
  onReschedule
}: RescheduleInterviewDialogProps) {
  if (!selectedInterview) return null;

  const candidate = mockCandidates.find(c => c.id === selectedInterview.candidateId);
  const selectedDate = rescheduleData.newDate ? new Date(rescheduleData.newDate) : undefined;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Reschedule Interview</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Interview Details */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-medium">{candidate?.firstName} {candidate?.lastName}</h4>
            <p className="text-sm text-gray-600">
              Current: {formatInterviewTime(selectedInterview)}
            </p>
          </div>

          {/* New Date */}
          <div>
            <Label>New Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, 'PPP') : 'Pick a new date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => onRescheduleChange('newDate', date ? format(date, 'yyyy-MM-dd') : '')}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* New Time */}
          <div>
            <Label htmlFor="newTime">New Time</Label>
            <Input
              id="newTime"
              type="time"
              value={rescheduleData.newTime}
              onChange={(e) => onRescheduleChange('newTime', e.target.value)}
            />
          </div>

          {/* Reason */}
          <div>
            <Label htmlFor="reason">Reason for Rescheduling</Label>
            <Textarea
              id="reason"
              placeholder="Please provide a reason for rescheduling..."
              value={rescheduleData.reason}
              onChange={(e) => onRescheduleChange('reason', e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={onReschedule}>
              <CalendarIcon className="mr-2 h-4 w-4" />
              Reschedule
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
