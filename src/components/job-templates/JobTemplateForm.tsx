import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { JobTemplateFormData } from '@/types';
import { 
  Building2, 
  Briefcase, 
  GraduationCap, 
  X, 
  Plus,
  FileText,
  DollarSign,
  Clock
} from 'lucide-react';

interface JobTemplateFormProps {
  initialData?: Partial<JobTemplateFormData>;
  onSubmit: (data: JobTemplateFormData) => void;
  onCancel: () => void;
  isEditing?: boolean;
}

export default function JobTemplateForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isEditing = false 
}: JobTemplateFormProps) {
  const [formData, setFormData] = useState<JobTemplateFormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    employmentType: initialData?.employmentType || '',
    requiredDocuments: initialData?.requiredDocuments || [],
    requiredLicenseYears: initialData?.requiredLicenseYears || '',
    clientBillRate: initialData?.clientBillRate || '',
    billRateCurrency: initialData?.billRateCurrency || 'USD',
    billRateFrequency: initialData?.billRateFrequency || 'hourly',
    industry: initialData?.industry || '',
    experienceYears: initialData?.experienceYears || '',
    skills: initialData?.skills || '',
    degreeRequirement: initialData?.degreeRequirement || '',
    evaluationTemplateId: initialData?.evaluationTemplateId || ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof JobTemplateFormData, string>>>({});
  const [newDocument, setNewDocument] = useState('');
  const [skillsArray, setSkillsArray] = useState<string[]>(
    initialData?.skills ? initialData.skills.split(',').map(s => s.trim()) : []
  );
  const [newSkill, setNewSkill] = useState('');

  const handleInputChange = (field: keyof JobTemplateFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const addDocument = () => {
    if (newDocument.trim() && !formData.requiredDocuments.includes(newDocument.trim())) {
      const updatedDocuments = [...formData.requiredDocuments, newDocument.trim()];
      setFormData(prev => ({ ...prev, requiredDocuments: updatedDocuments }));
      setNewDocument('');
    }
  };

  const removeDocument = (document: string) => {
    const updatedDocuments = formData.requiredDocuments.filter(doc => doc !== document);
    setFormData(prev => ({ ...prev, requiredDocuments: updatedDocuments }));
  };

  const addSkill = () => {
    if (newSkill.trim() && !skillsArray.includes(newSkill.trim())) {
      const updatedSkills = [...skillsArray, newSkill.trim()];
      setSkillsArray(updatedSkills);
      setFormData(prev => ({ ...prev, skills: updatedSkills.join(', ') }));
      setNewSkill('');
    }
  };

  const removeSkill = (skill: string) => {
    const updatedSkills = skillsArray.filter(s => s !== skill);
    setSkillsArray(updatedSkills);
    setFormData(prev => ({ ...prev, skills: updatedSkills.join(', ') }));
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof JobTemplateFormData, string>> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Job title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Job description is required';
    }

    if (!formData.employmentType) {
      newErrors.employmentType = 'Employment type is required';
    }

    if (!formData.industry) {
      newErrors.industry = 'Industry is required';
    }

    if (!formData.experienceYears) {
      newErrors.experienceYears = 'Experience years is required';
    } else if (isNaN(Number(formData.experienceYears)) || Number(formData.experienceYears) < 0) {
      newErrors.experienceYears = 'Please enter a valid number of years';
    }

    if (formData.clientBillRate && (isNaN(Number(formData.clientBillRate)) || Number(formData.clientBillRate) <= 0)) {
      newErrors.clientBillRate = 'Please enter a valid bill rate';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="organizational" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="organizational" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span className="hidden sm:inline">Organizational</span>
          </TabsTrigger>
          <TabsTrigger value="details" className="flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            <span className="hidden sm:inline">Job Details</span>
          </TabsTrigger>
          <TabsTrigger value="skills" className="flex items-center gap-2">
            <GraduationCap className="h-4 w-4" />
            <span className="hidden sm:inline">Skills</span>
          </TabsTrigger>
        </TabsList>

        {/* Organizational Information Tab */}
        <TabsContent value="organizational" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="mr-2 h-5 w-5" />
                Organizational Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Job Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="e.g. Senior Frontend Developer"
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && <p className="text-sm text-red-500 mt-1">{errors.title}</p>}
              </div>

              <div>
                <Label htmlFor="description">Job Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Detailed job description..."
                  rows={6}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && <p className="text-sm text-red-500 mt-1">{errors.description}</p>}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Job Details Tab */}
        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Briefcase className="mr-2 h-5 w-5" />
                Job Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="employmentType">Employment Type *</Label>
                  <Select value={formData.employmentType} onValueChange={(value) => handleInputChange('employmentType', value)}>
                    <SelectTrigger className={errors.employmentType ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select employment type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full-time">Full Time</SelectItem>
                      <SelectItem value="part-time">Part Time</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="internship">Internship</SelectItem>
                      <SelectItem value="temporary">Temporary</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.employmentType && <p className="text-sm text-red-500 mt-1">{errors.employmentType}</p>}
                </div>

                <div>
                  <Label htmlFor="requiredLicenseYears">Required License/Years</Label>
                  <Input
                    id="requiredLicenseYears"
                    value={formData.requiredLicenseYears}
                    onChange={(e) => handleInputChange('requiredLicenseYears', e.target.value)}
                    placeholder="e.g. 5+ years experience"
                  />
                </div>
              </div>

              <div>
                <Label>Required Documents</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={newDocument}
                    onChange={(e) => setNewDocument(e.target.value)}
                    placeholder="Add required document"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDocument())}
                  />
                  <Button type="button" onClick={addDocument} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.requiredDocuments.map((doc, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      {doc}
                      <button
                        type="button"
                        onClick={() => removeDocument(doc)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="clientBillRate">Client Bill Rate</Label>
                  <div className="flex">
                    <div className="flex items-center px-3 border border-r-0 rounded-l-md bg-gray-50">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                    </div>
                    <Input
                      id="clientBillRate"
                      value={formData.clientBillRate}
                      onChange={(e) => handleInputChange('clientBillRate', e.target.value)}
                      placeholder="85"
                      className={`rounded-l-none ${errors.clientBillRate ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.clientBillRate && <p className="text-sm text-red-500 mt-1">{errors.clientBillRate}</p>}
                </div>

                <div>
                  <Label htmlFor="billRateCurrency">Currency</Label>
                  <Select value={formData.billRateCurrency} onValueChange={(value) => handleInputChange('billRateCurrency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="INR">INR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="billRateFrequency">Frequency</Label>
                  <Select value={formData.billRateFrequency} onValueChange={(value) => handleInputChange('billRateFrequency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Skills Tab */}
        <TabsContent value="skills" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <GraduationCap className="mr-2 h-5 w-5" />
                Skills & Requirements
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="industry">Industry *</Label>
                  <Select value={formData.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                    <SelectTrigger className={errors.industry ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Technology">Technology</SelectItem>
                      <SelectItem value="Healthcare">Healthcare</SelectItem>
                      <SelectItem value="Finance">Finance</SelectItem>
                      <SelectItem value="Marketing">Marketing</SelectItem>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="Retail">Retail</SelectItem>
                      <SelectItem value="Consulting">Consulting</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.industry && <p className="text-sm text-red-500 mt-1">{errors.industry}</p>}
                </div>

                <div>
                  <Label htmlFor="experienceYears">Experience (Years) *</Label>
                  <div className="flex">
                    <Input
                      id="experienceYears"
                      value={formData.experienceYears}
                      onChange={(e) => handleInputChange('experienceYears', e.target.value)}
                      placeholder="5"
                      className={`rounded-r-none ${errors.experienceYears ? 'border-red-500' : ''}`}
                    />
                    <div className="flex items-center px-3 border border-l-0 rounded-r-md bg-gray-50">
                      <Clock className="h-4 w-4 text-gray-500 mr-1" />
                      <span className="text-sm text-gray-500">Years</span>
                    </div>
                  </div>
                  {errors.experienceYears && <p className="text-sm text-red-500 mt-1">{errors.experienceYears}</p>}
                </div>
              </div>

              <div>
                <Label>Skills</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={newSkill}
                    onChange={(e) => setNewSkill(e.target.value)}
                    placeholder="Add skill"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                  />
                  <Button type="button" onClick={addSkill} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {skillsArray.map((skill, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {skill}
                      <button
                        type="button"
                        onClick={() => removeSkill(skill)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="degreeRequirement">Degree Requirement</Label>
                  <Select value={formData.degreeRequirement} onValueChange={(value) => handleInputChange('degreeRequirement', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select degree requirement" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Degree Required</SelectItem>
                      <SelectItem value="high-school">High School</SelectItem>
                      <SelectItem value="associate">Associate Degree</SelectItem>
                      <SelectItem value="bachelor">Bachelor's Degree</SelectItem>
                      <SelectItem value="master">Master's Degree</SelectItem>
                      <SelectItem value="phd">PhD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="evaluationTemplateId">Evaluation Template</Label>
                  <Select value={formData.evaluationTemplateId} onValueChange={(value) => handleInputChange('evaluationTemplateId', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select evaluation template" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="eval-1">Technical Assessment</SelectItem>
                      <SelectItem value="eval-2">Behavioral Interview</SelectItem>
                      <SelectItem value="eval-3">Leadership Assessment</SelectItem>
                      <SelectItem value="eval-4">Cultural Fit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {isEditing ? 'Update Template' : 'Create Template'}
        </Button>
      </div>
    </form>
  );
}
