import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, Shield, AlertTriangle } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: {
    module: string;
    action: string;
  };
  requiredRole?: string;
  organizationType?: 'client' | 'vendor';
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  organizationType,
  fallback,
}) => {
  const location = useLocation();
  const {
    isAuthenticated,
    isLoading,
    user,
    hasPermission,
    isClientOrganization,
    isVendorOrganization,
    getUserRole,
  } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Authenticating...
            </h2>
            <p className="text-sm text-gray-600 text-center">
              Please wait while we verify your credentials
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate
        to="/login"
        state={{ from: location }}
        replace
      />
    );
  }

  // Check role requirement
  if (requiredRole && getUserRole() !== requiredRole) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Shield className="h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Access Denied
            </h2>
            <p className="text-sm text-gray-600 text-center mb-4">
              You don't have the required role ({requiredRole}) to access this page.
            </p>
            <p className="text-xs text-gray-500 text-center">
              Your current role: {getUserRole()}
            </p>
            {fallback && (
              <div className="mt-4 w-full">
                {fallback}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check permission requirement
  if (requiredPermission && !hasPermission(requiredPermission.module, requiredPermission.action)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Insufficient Permissions
            </h2>
            <p className="text-sm text-gray-600 text-center mb-4">
              You don't have permission to {requiredPermission.action} {requiredPermission.module}.
            </p>
            <p className="text-xs text-gray-500 text-center">
              Contact your administrator to request access.
            </p>
            {fallback && (
              <div className="mt-4 w-full">
                {fallback}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check organization type requirement
  if (organizationType) {
    const isCorrectOrgType = 
      (organizationType === 'client' && isClientOrganization()) ||
      (organizationType === 'vendor' && isVendorOrganization());

    if (!isCorrectOrgType) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
          <Card className="w-full max-w-md">
            <CardContent className="flex flex-col items-center justify-center p-8">
              <Shield className="h-12 w-12 text-blue-500 mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Organization Access Required
              </h2>
              <p className="text-sm text-gray-600 text-center mb-4">
                This page is only available for {organizationType} organizations.
              </p>
              <p className="text-xs text-gray-500 text-center">
                Your organization type: {isClientOrganization() ? 'client' : 'vendor'}
              </p>
              {fallback && (
                <div className="mt-4 w-full">
                  {fallback}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }
  }

  // All checks passed, render children
  return <>{children}</>;
};

// Higher-order component for easier usage
export const withProtectedRoute = <P extends object>(
  Component: React.ComponentType<P>,
  protection: Omit<ProtectedRouteProps, 'children'>
) => {
  return (props: P) => (
    <ProtectedRoute {...protection}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Specific protected route components for common use cases
export const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRole="admin">
    {children}
  </ProtectedRoute>
);

export const ClientRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute organizationType="client">
    {children}
  </ProtectedRoute>
);

export const VendorRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute organizationType="vendor">
    {children}
  </ProtectedRoute>
);

export const ManagerRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRole="manager">
    {children}
  </ProtectedRoute>
);

export default ProtectedRoute;
