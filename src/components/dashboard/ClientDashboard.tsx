import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import StatCard from './StatCard';
import CandidateList from './CandidateList';
import ProgressCard from './ProgressCard';
import { useClientDashboardData } from '@/hooks/useDashboardData';
import {
  Users,
  Briefcase,
  Calendar,
  TrendingUp,
  Building2,
  UserCheck,
  Clock,
  CheckCircle,
} from 'lucide-react';

const ClientDashboard: React.FC = () => {
  const navigate = useNavigate();
  const dashboardData = useClientDashboardData();

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading Dashboard...</h2>
          <p className="text-gray-600">Please wait while we load your dashboard.</p>
        </div>
      </div>
    );
  }

  const { organization, connectedVendors, stats, recentCandidates, onboardingCandidates } = dashboardData;

  // Convert onboarding candidates to progress items
  const onboardingProgress = onboardingCandidates.map(candidate => ({
    id: candidate.id,
    name: candidate.name,
    position: candidate.position,
    progress: Math.floor(Math.random() * 40) + 60, // Mock progress 60-100%
    status: 'onboarding',
    daysRemaining: Math.floor(Math.random() * 7) + 1,
  }));

  const handleViewAllCandidates = () => {
    navigate('/candidates');
  };

  const handleViewCandidate = (candidateId: string) => {
    navigate(`/candidates/${candidateId}`);
  };

  const handleViewAllJobs = () => {
    navigate('/jobs');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Client Dashboard</h1>
        <p className="text-gray-600">Manage your hiring process and vendor partnerships</p>
      </div>

      {/* Organization Info */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <p className="font-semibold text-gray-900">{organization.name}</p>
              <p className="text-sm text-gray-500">Client Organization</p>
            </div>
            <div className="ml-auto">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {stats.connectedVendorsCount} Vendors Connected
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Active Jobs"
          value={stats.activeJobs}
          change="+12%"
          changeType="positive"
          icon={Briefcase}
        />
        <StatCard
          title="Connected Vendors"
          value={stats.connectedVendorsCount}
          change="+2"
          changeType="positive"
          icon={Building2}
        />
        <StatCard
          title="Pending Reviews"
          value={stats.pendingReviews}
          change="+5"
          changeType="positive"
          icon={Clock}
        />
        <StatCard
          title="This Month Hires"
          value={stats.thisMonthHires}
          change="+18%"
          changeType="positive"
          icon={UserCheck}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Candidates */}
        <CandidateList
          title="Recent Candidates"
          candidates={recentCandidates}
          onViewAll={handleViewAllCandidates}
          onViewCandidate={handleViewCandidate}
          emptyMessage="No recent candidates"
        />

        {/* Onboarding Progress */}
        <ProgressCard
          title="Onboarding Progress"
          items={onboardingProgress}
          emptyMessage="No candidates in onboarding"
        />
      </div>

      {/* Connected Vendors */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Connected Vendors</CardTitle>
        </CardHeader>
        <CardContent>
          {connectedVendors.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No vendors connected yet</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {connectedVendors.map((vendor) => (
                <div
                  key={vendor.id}
                  className="p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">{vendor.name}</h3>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      Active
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500 mb-3">{vendor.description}</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">
                      {Math.floor(Math.random() * 20) + 5} submissions
                    </span>
                    <span className="text-green-600 font-medium">
                      {(4.0 + Math.random()).toFixed(1)} ★
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientDashboard;
