import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

interface ProgressItem {
  id: string;
  name: string;
  position: string;
  progress: number;
  status: string;
  daysRemaining?: number;
}

interface ProgressCardProps {
  title: string;
  items: ProgressItem[];
  emptyMessage?: string;
  showProgress?: boolean;
}

const ProgressCard: React.FC<ProgressCardProps> = ({
  title,
  items,
  emptyMessage = "No items to track",
  showProgress = true,
}) => {
  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
      case 'onboarding':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'delayed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {items.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>{emptyMessage}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {items.map((item) => (
              <div key={item.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {item.name}
                    </p>
                    <p className="text-sm text-gray-500 truncate">
                      {item.position}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant="secondary" 
                      className={getStatusColor(item.status)}
                    >
                      {item.status}
                    </Badge>
                    {item.daysRemaining !== undefined && (
                      <span className="text-xs text-gray-500">
                        {item.daysRemaining > 0 
                          ? `${item.daysRemaining} days left`
                          : 'Due today'
                        }
                      </span>
                    )}
                  </div>
                </div>
                {showProgress && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Progress</span>
                      <span>{item.progress}%</span>
                    </div>
                    <Progress 
                      value={item.progress} 
                      className="h-2"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProgressCard;
