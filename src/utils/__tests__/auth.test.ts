import { describe, it, expect, beforeEach } from 'vitest'
import { 
  getCurrentUser, 
  getCurrentOrganization, 
  switchUser, 
  isClientOrganization,
  isVendorOrganization,
  getUserRole,
  hasPermission,
  canAccessModule,
  getOrganizationUsers,
  getVendorOrganizations,
  getClientOrganizations
} from '../auth'

describe('Auth Utils', () => {
  beforeEach(() => {
    // Reset to default user before each test
    switchUser('user-1')
  })

  it('returns current user', () => {
    const user = getCurrentUser()
    expect(user).toBeDefined()
    expect(user.id).toBe('user-1')
    expect(user.firstName).toBe('Alice')
    expect(user.lastName).toBe('Johnson')
    expect(user.role).toBe('admin')
  })

  it('returns current organization', () => {
    const org = getCurrentOrganization()
    expect(org).toBeDefined()
    expect(org.id).toBe('org-1')
    expect(org.name).toBe('TechCorp Solutions')
    expect(org.type).toBe('client')
  })

  it('can switch users', () => {
    switchUser('user-3')
    const user = getCurrentUser()
    expect(user.id).toBe('user-3')
    expect(user.firstName).toBe('Sarah')
    expect(user.lastName).toBe('Wilson')
  })

  it('switches organization when switching users', () => {
    switchUser('user-3')
    const org = getCurrentOrganization()
    expect(org.id).toBe('org-2')
    expect(org.name).toBe('Elite Recruiters Inc')
    expect(org.type).toBe('vendor')
  })

  it('correctly identifies client organizations', () => {
    switchUser('user-1') // Client user
    expect(isClientOrganization()).toBe(true)

    switchUser('user-3') // Vendor user
    expect(isClientOrganization()).toBe(false)
  })

  it('correctly identifies vendor organizations', () => {
    switchUser('user-1') // Client user
    expect(isVendorOrganization()).toBe(false)
    
    switchUser('user-3') // Vendor user
    expect(isVendorOrganization()).toBe(true)
  })

  it('returns user role correctly', () => {
    switchUser('user-1')
    expect(getUserRole()).toBe('admin')
    
    switchUser('user-3')
    expect(getUserRole()).toBe('admin')
  })

  it('handles user role correctly', () => {
    const user = getCurrentUser()
    expect(['admin', 'recruiter', 'hiring_manager']).toContain(user.role)
  })

  it('returns user with all required properties', () => {
    const user = getCurrentUser()
    expect(user).toHaveProperty('id')
    expect(user).toHaveProperty('organizationId')
    expect(user).toHaveProperty('firstName')
    expect(user).toHaveProperty('lastName')
    expect(user).toHaveProperty('email')
    expect(user).toHaveProperty('role')
  })

  it('returns organization with all required properties', () => {
    const org = getCurrentOrganization()
    expect(org).toHaveProperty('id')
    expect(org).toHaveProperty('name')
    expect(org).toHaveProperty('type')
  })

  it('maintains user-organization relationship', () => {
    const user = getCurrentUser()
    const org = getCurrentOrganization()
    expect(user.organizationId).toBe(org.id)
  })

  it('handles invalid user switch gracefully', () => {
    switchUser('invalid-user-id')
    // Should fallback to default user or handle gracefully
    const user = getCurrentUser()
    expect(user).toBeDefined()
    expect(user.id).toBeDefined()
  })

  it('checks user permissions correctly', () => {
    switchUser('user-1') // Admin user should have permissions
    
    // Test permission checking
    const hasJobsRead = hasPermission('jobs', 'read')
    const hasJobsWrite = hasPermission('jobs', 'write')
    
    expect(typeof hasJobsRead).toBe('boolean')
    expect(typeof hasJobsWrite).toBe('boolean')
  })

  it('handles permission check for user without permissions', () => {
    switchUser('user-1')
    
    // Test non-existent permission
    const hasInvalidPermission = hasPermission('invalid-module', 'read')
    expect(hasInvalidPermission).toBe(false)
  })

  it('checks module access correctly', () => {
    switchUser('user-1')
    
    const canAccessJobs = canAccessModule('jobs')
    expect(typeof canAccessJobs).toBe('boolean')
  })

  it('gets organization users correctly', () => {
    const org1Users = getOrganizationUsers('org-1')
    const org2Users = getOrganizationUsers('org-2')
    
    expect(Array.isArray(org1Users)).toBe(true)
    expect(Array.isArray(org2Users)).toBe(true)
    expect(org1Users.length).toBeGreaterThan(0)
    expect(org2Users.length).toBeGreaterThan(0)
    
    // All users should belong to the requested organization
    org1Users.forEach(user => {
      expect(user.organizationId).toBe('org-1')
    })
  })

  it('gets vendor organizations correctly', () => {
    const vendorOrgs = getVendorOrganizations()
    
    expect(Array.isArray(vendorOrgs)).toBe(true)
    expect(vendorOrgs.length).toBeGreaterThan(0)
    
    // All organizations should be vendor type
    vendorOrgs.forEach(org => {
      expect(org.type).toBe('vendor')
    })
  })

  it('gets client organizations correctly', () => {
    const clientOrgs = getClientOrganizations()
    
    expect(Array.isArray(clientOrgs)).toBe(true)
    expect(clientOrgs.length).toBeGreaterThan(0)
    
    // All organizations should be client type
    clientOrgs.forEach(org => {
      expect(org.type).toBe('client')
    })
  })
})
