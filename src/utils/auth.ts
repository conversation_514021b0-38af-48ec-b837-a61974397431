import { User, Organization } from '@/types/multiTenant';
import { mockUsers, mockOrganizations } from '@/data/multiTenantData';

// Simulated authentication context
export interface AuthContext {
  user: User | null;
  organization: Organization | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  hasPermission: (module: string, action: string) => boolean;
  switchOrganization: (orgId: string) => boolean;
}

// Mock current user - in real app this would come from JWT/session
let currentUserId = 'user-1'; // Default to client user

export const getCurrentUser = (): User | null => {
  // For demo purposes, return the user based on currentUserId
  return mockUsers.find(user => user.id === currentUserId) || mockUsers[0];
};

// Helper function to switch users for testing
export const switchUser = (userId: string): void => {
  currentUserId = userId;
};

export const getCurrentOrganization = (): Organization | null => {
  const user = getCurrentUser();
  if (!user) return null;
  
  return mockOrganizations.find(org => org.id === user.organizationId) || null;
};

export const hasPermission = (module: string, action: 'create' | 'read' | 'update' | 'delete' | 'approve'): boolean => {
  const user = getCurrentUser();
  if (!user) return false;

  const permission = user.permissions.find(p => p.module === module);
  return permission ? permission.actions.includes(action) : false;
};

export const canAccessModule = (module: string): boolean => {
  return hasPermission(module, 'read');
};

export const isVendorOrganization = (): boolean => {
  const org = getCurrentOrganization();
  return org?.type === 'vendor';
};

export const isClientOrganization = (): boolean => {
  const org = getCurrentOrganization();
  return org?.type === 'client';
};

export const getUserRole = (): string => {
  const user = getCurrentUser();
  return user?.role || 'employee';
};

export const getOrganizationUsers = (orgId: string): User[] => {
  return mockUsers.filter(user => user.organizationId === orgId);
};

export const getVendorOrganizations = (): Organization[] => {
  return mockOrganizations.filter(org => org.type === 'vendor');
};

export const getClientOrganizations = (): Organization[] => {
  return mockOrganizations.filter(org => org.type === 'client');
};