// Security utilities and configurations

// Content Security Policy configuration
// Environment-based CSP directives for better security
const isDevelopment = import.meta.env.DEV;

export const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  'script-src': isDevelopment
    ? ["'self'", "'unsafe-inline'", "'unsafe-eval'"] // Allow unsafe directives in development for HMR
    : ["'self'", "'strict-dynamic'"], // Production: use strict-dynamic with nonces
  'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'], // unsafe-inline needed for CSS-in-JS
  'font-src': ["'self'", 'https://fonts.gstatic.com', 'https://fonts.googleapis.com'],
  'img-src': ["'self'", 'data:', 'https:', 'blob:'],
  'connect-src': [
    "'self'",
    'https://api.github.com',
    ...(isDevelopment ? ['ws:', 'wss:', 'http://localhost:*'] : []) // Allow WebSocket for dev server
  ],
  'frame-src': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
  'upgrade-insecure-requests': isDevelopment ? [] : [''],
};

// Generate CSP header string
export const generateCSPHeader = (): string => {
  return Object.entries(CSP_DIRECTIVES)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive;
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
};

// Environment-based security headers configuration
export const getSecurityHeaders = () => {
  const isDevelopment = import.meta.env.DEV;

  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=(), usb=()',
    ...(isDevelopment ? {} : {
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
      'Expect-CT': 'max-age=86400, enforce',
    }),
    'Content-Security-Policy': generateCSPHeader(),
  };
};

// Legacy export for backward compatibility
export const SECURITY_HEADERS = getSecurityHeaders();

// Rate limiting configuration
export const RATE_LIMITS = {
  // API endpoints rate limits (requests per minute)
  login: 5,
  register: 3,
  passwordReset: 3,
  candidateSubmission: 10,
  messagesSend: 20,
  search: 60,
  general: 100,
};

// Input validation and sanitization
export class SecurityValidator {
  // Check for common XSS patterns
  static containsXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
    ];
    
    return xssPatterns.some(pattern => pattern.test(input));
  }

  // Check for SQL injection patterns
  static containsSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\\b(OR|AND)\\s+\\d+\\s*=\\s*\\d+)/gi,
      /('|\\')|(;)|(--)|(\s)|(\/\*)|(\*\/)/gi,
    ];
    
    return sqlPatterns.some(pattern => pattern.test(input));
  }

  // Validate file upload
  static validateFileUpload(file: File): { valid: boolean; error?: string } {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
    ];

    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'File type not allowed' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'File size too large (max 10MB)' };
    }

    // Check file extension matches MIME type
    const extension = file.name.split('.').pop()?.toLowerCase();
    const mimeToExtension: Record<string, string[]> = {
      'application/pdf': ['pdf'],
      'application/msword': ['doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
      'text/plain': ['txt'],
      'image/jpeg': ['jpg', 'jpeg'],
      'image/png': ['png'],
      'image/gif': ['gif'],
    };

    const expectedExtensions = mimeToExtension[file.type];
    if (expectedExtensions && extension && !expectedExtensions.includes(extension)) {
      return { valid: false, error: 'File extension does not match file type' };
    }

    return { valid: true };
  }

  // Sanitize user input
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>"'&]/g, (match) => {
        const entities: Record<string, string> = {
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#x27;',
          '&': '&amp;',
        };
        return entities[match] || match;
      });
  }

  // Validate email format
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  // Validate phone number
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-()]{10,}$/;
    return phoneRegex.test(phone);
  }

  // Check password strength
  static validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return { valid: errors.length === 0, errors };
  }
}

// CSRF token management
export class CSRFProtection {
  private static token: string | null = null;

  static generateToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    this.token = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    return this.token;
  }

  static getToken(): string | null {
    return this.token;
  }

  static validateToken(token: string): boolean {
    return this.token === token;
  }

  static clearToken(): void {
    this.token = null;
  }
}

// Enhanced session management with secure storage
export class SessionManager {
  private static readonly SESSION_KEY = 'ats_session';
  private static readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private static readonly SECURE_SESSION_KEY = 'ats_secure_session';

  static setSession(userId: string, organizationId: string): void {
    const session = {
      userId,
      organizationId,
      timestamp: Date.now(),
      csrfToken: CSRFProtection.generateToken(),
      fingerprint: this.generateFingerprint(),
    };

    // Use secure storage when available
    if (this.supportsSecureStorage()) {
      this.setSecureSession(session);
    } else {
      // Fallback to sessionStorage (not localStorage for better security)
      sessionStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
    }
  }

  // Generate browser fingerprint for additional security
  private static generateFingerprint(): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Browser fingerprint', 2, 2);
    }

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
    ].join('|');

    return btoa(fingerprint).slice(0, 32);
  }

  // Check if secure storage is available
  private static supportsSecureStorage(): boolean {
    return !import.meta.env.DEV && 'crypto' in window && 'subtle' in window.crypto;
  }

  // Set session in secure storage
  private static setSecureSession(session: { userId: string; organizationId: string; timestamp: number; csrfToken: string; fingerprint: string }): void {
    // In a real implementation, this would encrypt the session data
    // For now, we'll use sessionStorage with additional security measures
    const encryptedSession = btoa(JSON.stringify(session));
    sessionStorage.setItem(this.SECURE_SESSION_KEY, encryptedSession);
  }

  static getSession(): { userId: string; organizationId: string; csrfToken: string; fingerprint?: string } | null {
    let sessionData: string | null = null;

    // Try secure storage first
    if (this.supportsSecureStorage()) {
      sessionData = sessionStorage.getItem(this.SECURE_SESSION_KEY);
      if (sessionData) {
        try {
          sessionData = atob(sessionData);
        } catch {
          sessionData = null;
        }
      }
    }

    // Fallback to regular session storage
    if (!sessionData) {
      sessionData = sessionStorage.getItem(this.SESSION_KEY);
    }

    if (!sessionData) return null;

    try {
      const session = JSON.parse(sessionData);

      // Check if session has expired
      if (Date.now() - session.timestamp > this.SESSION_TIMEOUT) {
        this.clearSession();
        return null;
      }

      // Validate fingerprint if available
      if (session.fingerprint && session.fingerprint !== this.generateFingerprint()) {
        console.warn('Session fingerprint mismatch - possible session hijacking attempt');
        this.clearSession();
        return null;
      }

      return session;
    } catch {
      this.clearSession();
      return null;
    }
  }

  static clearSession(): void {
    // Clear from all possible storage locations
    sessionStorage.removeItem(this.SESSION_KEY);
    sessionStorage.removeItem(this.SECURE_SESSION_KEY);
    localStorage.removeItem(this.SESSION_KEY); // Legacy cleanup
    CSRFProtection.clearToken();
  }

  static isSessionValid(): boolean {
    return this.getSession() !== null;
  }
}

// Audit logging
export class AuditLogger {
  static log(action: string, userId: string, details: Record<string, unknown> = {}): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      action,
      userId,
      userAgent: navigator.userAgent,
      ip: 'client-side', // In real app, this would come from server
      details,
    };

    // In production, send to logging service
    console.log('AUDIT LOG:', logEntry);
    
    // Store in localStorage for demo (in production, send to server)
    const logs = JSON.parse(localStorage.getItem('audit_logs') || '[]');
    logs.push(logEntry);
    
    // Keep only last 100 logs
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }
    
    localStorage.setItem('audit_logs', JSON.stringify(logs));
  }

  static getLogs(): unknown[] {
    return JSON.parse(localStorage.getItem('audit_logs') || '[]');
  }
}
