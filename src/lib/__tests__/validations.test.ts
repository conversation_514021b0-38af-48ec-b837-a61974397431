import { describe, it, expect } from 'vitest'
import {
  userSchema,
  candidateSchema,
  jobSchema,
  organizationSchema,
  feedbackSchema,
  validateInput,
  sanitizeHtml,
  sanitizeInput
} from '../validations'

describe('Validation Schemas', () => {
  describe('userSchema', () => {
    it('validates valid user data', () => {
      const validUser = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        title: 'Software Engineer',
        department: 'Engineering'
      }
      
      expect(() => userSchema.parse(validUser)).not.toThrow()
    })

    it('rejects invalid email', () => {
      const invalidUser = {
        firstName: 'John',
        lastName: 'Doe',
        email: 'invalid-email',
        title: 'Software Engineer'
      }
      
      expect(() => userSchema.parse(invalidUser)).toThrow()
    })
  })

  describe('candidateSchema', () => {
    it('validates valid candidate data', () => {
      const validCandidate = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567890',
        position: 'Frontend Developer',
        department: 'Engineering',
        experience: 5,
        skills: ['React', 'TypeScript', 'CSS']
      }
      
      expect(() => candidateSchema.parse(validCandidate)).not.toThrow()
    })

    it('rejects negative experience', () => {
      const invalidCandidate = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567890',
        position: 'Developer',
        department: 'Engineering',
        experience: -1,
        skills: ['React']
      }
      
      expect(() => candidateSchema.parse(invalidCandidate)).toThrow()
    })
  })

  describe('jobSchema', () => {
    it('validates valid job data', () => {
      const validJob = {
        title: 'Senior Developer',
        department: 'Engineering',
        location: 'San Francisco, CA',
        type: 'full-time' as const,
        description: 'We are looking for a senior developer...',
        requirements: ['5+ years experience', 'React knowledge']
      }
      
      expect(() => jobSchema.parse(validJob)).not.toThrow()
    })

    it('rejects invalid job type', () => {
      const invalidJob = {
        title: 'Developer',
        department: 'Engineering',
        location: 'SF',
        type: 'invalid-type',
        description: 'Job description',
        requirements: ['Experience']
      }
      
      expect(() => jobSchema.parse(invalidJob)).toThrow()
    })
  })

  describe('organizationSchema', () => {
    it('validates valid organization data', () => {
      const validOrg = {
        name: 'TechCorp',
        type: 'client' as const,
        industry: 'Technology',
        size: '100-500',
        address: '123 Main St, SF, CA',
        phone: '+1234567890',
        email: '<EMAIL>'
      }
      
      expect(() => organizationSchema.parse(validOrg)).not.toThrow()
    })
  })

  describe('feedbackSchema', () => {
    it('validates valid feedback data', () => {
      const validFeedback = {
        rating: 4,
        notes: 'Great candidate',
        recommendation: 'hire' as const
      }
      
      expect(() => feedbackSchema.parse(validFeedback)).not.toThrow()
    })

    it('rejects rating outside valid range', () => {
      const invalidFeedback = {
        rating: 6,
        notes: 'Great candidate'
      }
      
      expect(() => feedbackSchema.parse(invalidFeedback)).toThrow()
    })
  })
})

describe('Utility Functions', () => {
  describe('validateInput', () => {
    it('returns success for valid data', () => {
      const validUser = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        title: 'Developer'
      }
      
      const result = validateInput(userSchema, validUser)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validUser)
      }
    })

    it('returns errors for invalid data', () => {
      const invalidUser = {
        firstName: '',
        lastName: 'Doe',
        email: 'invalid-email',
        title: 'Developer'
      }
      
      const result = validateInput(userSchema, invalidUser)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.errors).toBeInstanceOf(Array)
        expect(result.errors.length).toBeGreaterThan(0)
      }
    })
  })

  describe('sanitizeHtml', () => {
    it('escapes HTML characters', () => {
      const input = '<script>alert("xss")</script>'
      const result = sanitizeHtml(input)
      expect(result).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;')
    })

    it('handles empty string', () => {
      const result = sanitizeHtml('')
      expect(result).toBe('')
    })
  })

  describe('sanitizeInput', () => {
    it('removes dangerous characters', () => {
      const input = '<script>"dangerous"</script>'
      const result = sanitizeInput(input)
      expect(result).toBe('scriptdangerous/script')
    })

    it('trims whitespace', () => {
      const input = '  hello world  '
      const result = sanitizeInput(input)
      expect(result).toBe('hello world')
    })

    it('limits length to 1000 characters', () => {
      const input = 'a'.repeat(1500)
      const result = sanitizeInput(input)
      expect(result.length).toBe(1000)
    })
  })
})
