import { describe, it, expect } from 'vitest'
import { render } from '../test/utils'

describe('Basic Test Suite', () => {
  it('should pass a simple test', () => {
    expect(1 + 1).toBe(2)
  })

  it('should render a simple component', () => {
    const TestComponent = () => <div>Hello Test</div>
    const { container } = render(<TestComponent />)
    
    expect(container.textContent).toContain('Hello Test')
  })

  it('should validate email format', () => {
    expect('<EMAIL>').toBeValidEmail()
    expect('invalid-email').not.toBeValidEmail()
  })

  it('should validate phone format', () => {
    expect('+1234567890').toBeValidPhone()
    expect('123').not.toBeValidPhone()
  })
})
