import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// Enable MSW in development
async function enableMocking() {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  const { worker } = await import('./test/mocks/browser')

  // Start the worker with quiet mode to reduce console noise
  return worker.start({
    onUnhandledRequest: 'bypass',
    quiet: true,
  })
}

enableMocking().then(() => {
  createRoot(document.getElementById('root')!).render(<App />);
});
