// Environment-based security configuration
export interface SecurityConfig {
  // Authentication settings
  auth: {
    tokenExpiry: number;
    refreshTokenExpiry: number;
    sessionTimeout: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };
  
  // Cookie settings
  cookies: {
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
    httpOnly: boolean;
    domain?: string;
  };
  
  // CSP settings
  csp: {
    enableStrictDynamic: boolean;
    allowUnsafeInline: boolean;
    allowUnsafeEval: boolean;
    reportUri?: string;
  };
  
  // Rate limiting
  rateLimit: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
  };
  
  // Encryption
  encryption: {
    algorithm: string;
    keyLength: number;
    ivLength: number;
  };
}

// Development configuration
const developmentConfig: SecurityConfig = {
  auth: {
    tokenExpiry: 15 * 60 * 1000, // 15 minutes
    refreshTokenExpiry: 7 * 24 * 60 * 60 * 1000, // 7 days
    sessionTimeout: 60 * 60 * 1000, // 1 hour (longer for dev)
    maxLoginAttempts: 10, // More lenient for dev
    lockoutDuration: 5 * 60 * 1000, // 5 minutes
  },
  cookies: {
    secure: false, // HTTP allowed in development
    sameSite: 'lax',
    httpOnly: false, // Allow client-side access for debugging
  },
  csp: {
    enableStrictDynamic: false,
    allowUnsafeInline: true, // Required for HMR and dev tools
    allowUnsafeEval: true, // Required for dev tools
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // Very high for development
    skipSuccessfulRequests: true,
  },
  encryption: {
    algorithm: 'AES-GCM',
    keyLength: 256,
    ivLength: 12,
  },
};

// Production configuration
const productionConfig: SecurityConfig = {
  auth: {
    tokenExpiry: 15 * 60 * 1000, // 15 minutes
    refreshTokenExpiry: 7 * 24 * 60 * 60 * 1000, // 7 days
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxLoginAttempts: 5, // Strict limit
    lockoutDuration: 30 * 60 * 1000, // 30 minutes
  },
  cookies: {
    secure: true, // HTTPS only
    sameSite: 'strict',
    httpOnly: true, // Server-side only
    domain: import.meta.env.VITE_COOKIE_DOMAIN,
  },
  csp: {
    enableStrictDynamic: true,
    allowUnsafeInline: false, // Strict CSP
    allowUnsafeEval: false, // No eval() allowed
    reportUri: import.meta.env.VITE_CSP_REPORT_URI,
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // Conservative limit
    skipSuccessfulRequests: false,
  },
  encryption: {
    algorithm: 'AES-GCM',
    keyLength: 256,
    ivLength: 12,
  },
};

// Test configuration
const testConfig: SecurityConfig = {
  ...developmentConfig,
  auth: {
    ...developmentConfig.auth,
    sessionTimeout: 5 * 60 * 1000, // 5 minutes for faster tests
    maxLoginAttempts: 3,
    lockoutDuration: 1 * 60 * 1000, // 1 minute
  },
  rateLimit: {
    ...developmentConfig.rateLimit,
    maxRequests: 50, // Lower for tests
  },
};

// Get configuration based on environment
export const getSecurityConfig = (): SecurityConfig => {
  const env = import.meta.env.MODE;
  
  switch (env) {
    case 'production':
      return productionConfig;
    case 'test':
      return testConfig;
    case 'development':
    default:
      return developmentConfig;
  }
};

// Export current configuration
export const securityConfig = getSecurityConfig();

// Utility functions
export const isProduction = () => import.meta.env.PROD;
export const isDevelopment = () => import.meta.env.DEV;
export const isTest = () => import.meta.env.MODE === 'test';

// Security feature flags
export const securityFeatures = {
  enableCSRFProtection: true,
  enableRateLimiting: isProduction(),
  enableSessionFingerprinting: isProduction(),
  enableSecureCookies: isProduction(),
  enableContentSecurityPolicy: true,
  enableHSTS: isProduction(),
  enableXSSProtection: true,
  enableClickjackingProtection: true,
  enableMIMETypeSniffingProtection: true,
  enableReferrerPolicy: true,
  enablePermissionsPolicy: true,
};

// Validation functions
export const validateSecurityConfig = (config: SecurityConfig): boolean => {
  // Validate token expiry times
  if (config.auth.tokenExpiry >= config.auth.refreshTokenExpiry) {
    console.error('Token expiry must be less than refresh token expiry');
    return false;
  }
  
  // Validate session timeout
  if (config.auth.sessionTimeout < config.auth.tokenExpiry) {
    console.error('Session timeout must be greater than or equal to token expiry');
    return false;
  }
  
  // Validate rate limiting
  if (config.rateLimit.maxRequests <= 0) {
    console.error('Rate limit max requests must be greater than 0');
    return false;
  }
  
  // Validate encryption settings
  if (![128, 192, 256].includes(config.encryption.keyLength)) {
    console.error('Invalid encryption key length');
    return false;
  }
  
  return true;
};

// Initialize and validate configuration
if (!validateSecurityConfig(securityConfig)) {
  throw new Error('Invalid security configuration');
}
