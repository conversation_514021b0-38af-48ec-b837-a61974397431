import { useAuth } from '@/contexts/AuthContext';
import ClientDashboard from '@/components/dashboard/ClientDashboard';
import VendorDashboard from '@/components/dashboard/VendorDashboard';
import DefaultDashboard from '@/components/dashboard/DefaultDashboard';

export default function Dashboard() {
  const { 
    user, 
    organization, 
    isLoading, 
    isClientOrganization, 
    isVendorOrganization 
  } = useAuth();

  // Add loading state and error handling
  if (isLoading || !user || !organization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading Dashboard...</h2>
          <p className="text-gray-600">Please wait while we load your dashboard.</p>
        </div>
      </div>
    );
  }

  const isClient = isClientOrganization();
  const isVendor = isVendorOrganization();

  // Render different dashboards based on organization type
  if (isClient) {
    return <ClientDashboard />;
  } else if (isVendor) {
    return <VendorDashboard />;
  } else {
    return <DefaultDashboard />;
  }
}
