import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { mockJobs } from '@/data/mockData';
import { mockOrganizations } from '@/data/multiTenantData';
import { useAuth } from '@/contexts/AuthContext';
import {
  ArrowLeft,
  Upload,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  GraduationCap,
  DollarSign,
  Calendar,
  FileText,
  CheckCircle
} from 'lucide-react';

export default function CandidateSubmission() {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const { user, organization, isVendorOrganization, isLoading } = useAuth();

  // Initialize state before any early returns
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    location: '',
    currentTitle: '',
    currentCompany: '',
    experience: '',
    education: '',
    skills: '',
    expectedSalary: '',
    noticePeriod: '',
    availability: '',
    vendorNotes: '',
    resumeFile: null as File | null,
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading...</h2>
          <p className="text-gray-600">Please wait while we load the submission form.</p>
        </div>
      </div>
    );
  }

  // Only allow vendor organizations
  if (!isVendorOrganization()) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Access Denied</h2>
          <p className="text-gray-600">This page is only available to vendor organizations.</p>
        </div>
      </div>
    );
  }

  // Find the job
  const job = mockJobs.find(j => j.id === jobId);
  const clientOrg = mockOrganizations.find(org => org.type === 'client');

  if (!job) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Job Not Found</h2>
          <p className="text-gray-600">The requested job could not be found.</p>
          <Button onClick={() => navigate('/vendor-jobs')} className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to My Jobs
          </Button>
        </div>
      </div>
    );
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, resumeFile: file }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In real app, this would submit to API
    console.log('Submitting candidate:', formData);
    
    // Show success message and redirect
    alert('Candidate submitted successfully! The client will review and provide feedback.');
    navigate('/vendor-jobs');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button 
          variant="outline" 
          onClick={() => navigate('/vendor-jobs')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to My Jobs
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Submit Candidate</h1>
          <p className="text-gray-600">Submit a candidate for {job.title}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Job Details Sidebar */}
        <div className="lg:col-span-1">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle className="text-lg">Job Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900">{job.title}</h3>
                <div className="flex items-center text-sm text-gray-600 mt-1">
                  <Briefcase className="mr-1 h-3 w-3" />
                  {clientOrg?.name}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="mr-1 h-3 w-3" />
                  {job.location}
                </div>
              </div>
              
              <div>
                <Badge className="bg-blue-100 text-blue-800">
                  {job.type}
                </Badge>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Key Requirements:</h4>
                <div className="space-y-1">
                  {job.requirements.slice(0, 5).map((req, index) => (
                    <div key={index} className="text-xs text-gray-600 flex items-start">
                      <CheckCircle className="mr-1 h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                      {req}
                    </div>
                  ))}
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="text-xs text-gray-500">
                  <div>Posted: {new Date(job.postedDate).toLocaleDateString()}</div>
                  <div>Department: {job.department}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Candidate Submission Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone *</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="location">Current Location *</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="City, State/Country"
                    required
                  />
                </div>
              </CardContent>
            </Card>

            {/* Professional Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Briefcase className="mr-2 h-5 w-5" />
                  Professional Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="currentTitle">Current Title *</Label>
                    <Input
                      id="currentTitle"
                      value={formData.currentTitle}
                      onChange={(e) => handleInputChange('currentTitle', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="currentCompany">Current Company</Label>
                    <Input
                      id="currentCompany"
                      value={formData.currentCompany}
                      onChange={(e) => handleInputChange('currentCompany', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="experience">Years of Experience *</Label>
                  <Select value={formData.experience} onValueChange={(value) => handleInputChange('experience', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select experience level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0-1">0-1 years</SelectItem>
                      <SelectItem value="2-3">2-3 years</SelectItem>
                      <SelectItem value="4-5">4-5 years</SelectItem>
                      <SelectItem value="6-8">6-8 years</SelectItem>
                      <SelectItem value="9-12">9-12 years</SelectItem>
                      <SelectItem value="13+">13+ years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="skills">Key Skills *</Label>
                  <Textarea
                    id="skills"
                    value={formData.skills}
                    onChange={(e) => handleInputChange('skills', e.target.value)}
                    placeholder="React, TypeScript, Node.js, AWS, etc."
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="education">Education</Label>
                  <Input
                    id="education"
                    value={formData.education}
                    onChange={(e) => handleInputChange('education', e.target.value)}
                    placeholder="Degree, University, Year"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Compensation & Availability */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="mr-2 h-5 w-5" />
                  Compensation & Availability
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="expectedSalary">Expected Salary</Label>
                    <Input
                      id="expectedSalary"
                      value={formData.expectedSalary}
                      onChange={(e) => handleInputChange('expectedSalary', e.target.value)}
                      placeholder="$120,000 - $140,000"
                    />
                  </div>
                  <div>
                    <Label htmlFor="noticePeriod">Notice Period</Label>
                    <Select value={formData.noticePeriod} onValueChange={(value) => handleInputChange('noticePeriod', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select notice period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="immediate">Immediate</SelectItem>
                        <SelectItem value="1-week">1 week</SelectItem>
                        <SelectItem value="2-weeks">2 weeks</SelectItem>
                        <SelectItem value="1-month">1 month</SelectItem>
                        <SelectItem value="2-months">2 months</SelectItem>
                        <SelectItem value="3-months">3 months</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="availability">Availability for Interview</Label>
                  <Textarea
                    id="availability"
                    value={formData.availability}
                    onChange={(e) => handleInputChange('availability', e.target.value)}
                    placeholder="Available weekdays 9 AM - 5 PM EST, flexible for urgent interviews"
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Resume Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Resume Upload
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <Label htmlFor="resume" className="cursor-pointer">
                      <span className="text-blue-600 hover:text-blue-500">Upload resume</span>
                      <span className="text-gray-600"> or drag and drop</span>
                    </Label>
                    <Input
                      id="resume"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2">PDF, DOC, DOCX up to 10MB</p>
                  {formData.resumeFile && (
                    <div className="mt-2 text-sm text-green-600">
                      ✓ {formData.resumeFile.name}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Vendor Notes */}
            <Card>
              <CardHeader>
                <CardTitle>Additional Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label htmlFor="vendorNotes">Notes for Client</Label>
                  <Textarea
                    id="vendorNotes"
                    value={formData.vendorNotes}
                    onChange={(e) => handleInputChange('vendorNotes', e.target.value)}
                    placeholder="Any additional information about this candidate that would be helpful for the client..."
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className="flex space-x-4">
              <Button type="submit" className="flex-1">
                Submit Candidate
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => navigate('/vendor-jobs')}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
