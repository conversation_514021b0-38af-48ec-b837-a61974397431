import { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { mockJobs, mockCandidates } from '@/data/mockData';
import { mockOrganizations, mockVendorClientRelationships } from '@/data/multiTenantData';
import { Job } from '@/types';
import { getCurrentOrganization, isClientOrganization } from '@/utils/auth';
import {
  Plus,
  Search,
  MapPin,
  Calendar,
  Users,
  Briefcase,
  Edit,
  Eye,
  UserPlus,
  Building2
} from 'lucide-react';

const getJobTypeColor = (type: string) => {
  switch (type) {
    case 'full-time':
      return 'bg-green-100 text-green-800';
    case 'part-time':
      return 'bg-blue-100 text-blue-800';
    case 'contract':
      return 'bg-orange-100 text-orange-800';
    case 'internship':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function Jobs() {
  const [jobs] = useState<Job[]>(mockJobs);
  const [searchTerm, setSearchTerm] = useState('');
  const [assignVendorJob, setAssignVendorJob] = useState<Job | null>(null);
  const currentOrg = getCurrentOrganization();
  const isClient = isClientOrganization();

  const filteredJobs = jobs.filter(job =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getCandidateCount = (jobTitle: string) => {
    return mockCandidates.filter(candidate => candidate.position === jobTitle).length;
  };

  // Get connected vendors for the current client organization
  const getConnectedVendors = () => {
    if (!isClient || !currentOrg) return [];

    const approvedRelationships = mockVendorClientRelationships.filter(
      rel => rel.clientId === currentOrg.id && rel.status === 'approved'
    );

    return mockOrganizations.filter(org =>
      org.type === 'vendor' &&
      approvedRelationships.some(rel => rel.vendorId === org.id)
    );
  };

  const connectedVendors = getConnectedVendors();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Job Openings</h1>
          <p className="text-gray-600">Manage your active job postings</p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Post Job
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Job Posting</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Job Title</Label>
                  <Input id="title" placeholder="e.g. Senior Frontend Developer" />
                </div>
                <div>
                  <Label htmlFor="department">Department</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="product">Product</SelectItem>
                      <SelectItem value="design">Design</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" placeholder="e.g. San Francisco, CA" />
                </div>
                <div>
                  <Label htmlFor="type">Employment Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full-time">Full-time</SelectItem>
                      <SelectItem value="part-time">Part-time</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="internship">Internship</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="description">Job Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the role, responsibilities, and what you're looking for..."
                  className="h-24"
                />
              </div>
              <div>
                <Label htmlFor="requirements">Requirements</Label>
                <Textarea
                  id="requirements"
                  placeholder="List the key requirements and qualifications..."
                  className="h-20"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline">Save as Draft</Button>
                <Button>Publish Job</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search jobs by title, department, or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Jobs Grid */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
        {filteredJobs.map((job) => (
          <Card key={job.id} className="hover:shadow-md transition-shadow flex flex-col h-full">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div className="space-y-2 flex-1 min-w-0">
                  <CardTitle className="text-lg leading-tight">{job.title}</CardTitle>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Briefcase className="mr-1 h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{job.department}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="mr-1 h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{job.location}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 flex-shrink-0 ml-4">
                  <Badge className={getJobTypeColor(job.type)}>
                    {job.type}
                  </Badge>
                  {job.isActive && (
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      Active
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex flex-col flex-1">
              <div className="flex-1">
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                  {job.description}
                </p>

                <div className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Key Requirements:</h4>
                    <div className="flex flex-wrap gap-1">
                      {job.requirements.slice(0, 3).map((req, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {req}
                        </Badge>
                      ))}
                      {job.requirements.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{job.requirements.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Fixed bottom section with consistent alignment */}
              <div className="mt-6 pt-4 border-t">
                <div className="flex flex-col space-y-3">
                  <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-3 w-3 flex-shrink-0" />
                      <span>Posted {new Date(job.postedDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="mr-1 h-3 w-3 flex-shrink-0" />
                      <span>{getCandidateCount(job.title)} applicants</span>
                    </div>
                    {isClient && (
                      <div className="flex items-center">
                        <Building2 className="mr-1 h-3 w-3 flex-shrink-0" />
                        <span>{Math.floor(Math.random() * 3)} vendors assigned</span>
                      </div>
                    )}
                  </div>

                  {/* Action buttons with consistent sizing */}
                  <div className="flex justify-end space-x-2">
                    {isClient && (
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => setAssignVendorJob(job)}
                            className="bg-blue-600 hover:bg-blue-700 h-8 px-3"
                          >
                            <UserPlus className="mr-1 h-3 w-3" />
                            Assign Vendors
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Assign Vendors to {job.title}</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Select Vendors to Assign</Label>
                              <div className="mt-2 space-y-2 max-h-64 overflow-y-auto">
                                {connectedVendors.map((vendor) => (
                                  <div key={vendor.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                                    <input
                                      type="checkbox"
                                      id={`vendor-${vendor.id}`}
                                      className="rounded border-gray-300"
                                    />
                                    <div className="flex-1">
                                      <div className="flex items-center space-x-2">
                                        <Building2 className="h-4 w-4 text-gray-400" />
                                        <span className="font-medium">{vendor.name}</span>
                                      </div>
                                      <div className="text-sm text-gray-600">
                                        {vendor.vendorProfile?.specializations.join(', ')}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        Success Rate: {vendor.vendorProfile?.successRate}% |
                                        Avg Time: {vendor.vendorProfile?.averageTimeToFill} days
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="deadline">Submission Deadline</Label>
                              <Input
                                id="deadline"
                                type="date"
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor="instructions">Instructions for Vendors</Label>
                              <Textarea
                                id="instructions"
                                placeholder="Please submit candidates with strong React and TypeScript experience..."
                                rows={3}
                                className="mt-1"
                              />
                            </div>
                            <div className="flex space-x-2">
                              <Button>Assign Selected Vendors</Button>
                              <Button variant="outline" onClick={() => setAssignVendorJob(null)}>
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    )}

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button size="sm" variant="outline" className="h-8 px-3">
                          <Eye className="mr-1 h-3 w-3" />
                          View
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>{job.title}</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Department:</span>
                              <p className="font-medium">{job.department}</p>
                            </div>
                            <div>
                              <span className="text-gray-500">Location:</span>
                              <p className="font-medium">{job.location}</p>
                            </div>
                            <div>
                              <span className="text-gray-500">Type:</span>
                              <p className="font-medium capitalize">{job.type}</p>
                            </div>
                            <div>
                              <span className="text-gray-500">Posted:</span>
                              <p className="font-medium">{new Date(job.postedDate).toLocaleDateString()}</p>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-medium mb-2">Description</h4>
                            <p className="text-sm text-gray-600">{job.description}</p>
                          </div>
                          
                          <div>
                            <h4 className="font-medium mb-2">Requirements</h4>
                            <ul className="text-sm text-gray-600 space-y-1">
                              {job.requirements.map((req, index) => (
                                <li key={index} className="flex items-center">
                                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></span>
                                  {req}
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div className="flex justify-between items-center pt-4 border-t">
                            <div className="text-sm text-gray-500">
                              {getCandidateCount(job.title)} candidates applied
                            </div>
                            <div className="flex space-x-2">
                              <Button variant="outline">Edit</Button>
                              <Button>View Candidates</Button>
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button size="sm" className="h-8 px-3">
                      <Edit className="mr-1 h-3 w-3" />
                      Edit
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}