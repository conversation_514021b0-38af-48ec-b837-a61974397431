import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { mockInterviews } from '@/data/mockData';
import { useAuth } from '@/contexts/AuthContext';
import { Interview } from '@/types';
import { isSameDay, parseISO } from 'date-fns';
import { Plus } from 'lucide-react';
import {
  TodaysInterviews,
  InterviewListView,
  InterviewCalendarView,
  InterviewTimelineView,
  ScheduleInterviewDialog,
  InterviewFeedbackDialog,
  RescheduleInterviewDialog
} from '@/components/interviews';

export default function Interviews() {
  const [interviews] = useState<Interview[]>(mockInterviews);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);
  const [isFeedbackDialogOpen, setIsFeedbackDialogOpen] = useState(false);
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'timeline'>('list');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [newInterview, setNewInterview] = useState({
    candidateId: '',
    type: 'video',
    date: '',
    time: '',
    duration: '60',
    location: '',
    notes: '',
    interviewers: [] as string[],
    meetingLink: '',
    agenda: ''
  });
  const [feedback, setFeedback] = useState({
    rating: 0,
    notes: '',
    recommendation: '',
    strengths: '',
    concerns: '',
    nextSteps: ''
  });
  const [rescheduleData, setRescheduleData] = useState({
    newDate: '',
    newTime: '',
    reason: ''
  });

  const { isClientOrganization, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading Interviews...</h2>
          <p className="text-gray-600">Please wait while we load your interview data.</p>
        </div>
      </div>
    );
  }

  const isClient = isClientOrganization();

  // Filter interviews based on status
  const filteredInterviews = interviews.filter(interview => {
    if (filterStatus !== 'all' && interview.status !== filterStatus) return false;
    return true;
  });

  const upcomingInterviews = filteredInterviews.filter(interview => interview.status === 'scheduled');
  const completedInterviews = filteredInterviews.filter(interview => interview.status === 'completed');
  const todayInterviews = filteredInterviews.filter(interview => {
    if (!interview.date) return false;
    try {
      return isSameDay(parseISO(interview.date), new Date());
    } catch {
      return false;
    }
  });

  // Event handlers
  const handleScheduleInterview = () => {
    console.log('Scheduling interview:', newInterview);
    setNewInterview({
      candidateId: '',
      type: 'video',
      date: '',
      time: '',
      duration: '60',
      location: '',
      notes: '',
      interviewers: [],
      meetingLink: '',
      agenda: ''
    });
    setIsScheduleDialogOpen(false);
  };

  const handleReschedule = (interview: Interview) => {
    setSelectedInterview(interview);
    setRescheduleData({
      newDate: '',
      newTime: '',
      reason: ''
    });
    setIsRescheduleDialogOpen(true);
  };

  const handleProvideFeedback = (interview: Interview) => {
    setSelectedInterview(interview);
    setIsFeedbackDialogOpen(true);
  };

  const handleSubmitFeedback = () => {
    console.log('Submitting feedback:', feedback);
    setFeedback({
      rating: 0,
      notes: '',
      recommendation: '',
      strengths: '',
      concerns: '',
      nextSteps: ''
    });
    setIsFeedbackDialogOpen(false);
  };

  const handleRescheduleSubmit = () => {
    console.log('Rescheduling interview:', selectedInterview, rescheduleData);
    setRescheduleData({
      newDate: '',
      newTime: '',
      reason: ''
    });
    setIsRescheduleDialogOpen(false);
  };

  const handleInterviewChange = (field: string, value: string | string[]) => {
    setNewInterview(prev => ({ ...prev, [field]: value }));
  };

  const handleFeedbackChange = (field: string, value: string | number) => {
    setFeedback(prev => ({ ...prev, [field]: value }));
  };

  const handleRescheduleChange = (field: string, value: string) => {
    setRescheduleData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Interview Management</h1>
          <p className="text-gray-600">
            {isClient ? 'Schedule and manage candidate interviews' : 'View your scheduled interviews and provide feedback'}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Status Filter */}
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Interviews</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          {/* Schedule Interview Button */}
          {isClient && (
            <Button onClick={() => setIsScheduleDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Schedule Interview
            </Button>
          )}
        </div>
      </div>

      {/* Today's Interviews */}
      {todayInterviews.length > 0 && (
        <TodaysInterviews
          interviews={todayInterviews}
          isClient={isClient}
          onProvideFeedback={handleProvideFeedback}
          onReschedule={handleReschedule}
        />
      )}

      {/* Main Content with Tabs */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'list' | 'calendar' | 'timeline')} className="space-y-6">
        {/* View Mode Toggle */}
        <TabsList>
          <TabsTrigger value="list">List</TabsTrigger>
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          <InterviewListView
            upcomingInterviews={upcomingInterviews}
            completedInterviews={completedInterviews}
            isClient={isClient}
            onProvideFeedback={handleProvideFeedback}
            onReschedule={handleReschedule}
          />
        </TabsContent>

        <TabsContent value="calendar" className="space-y-6">
          <InterviewCalendarView
            interviews={filteredInterviews}
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          <InterviewTimelineView
            interviews={filteredInterviews}
            isClient={isClient}
            onProvideFeedback={handleProvideFeedback}
            onReschedule={handleReschedule}
          />
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <ScheduleInterviewDialog
        isOpen={isScheduleDialogOpen}
        onClose={() => setIsScheduleDialogOpen(false)}
        newInterview={newInterview}
        onInterviewChange={handleInterviewChange}
        onSchedule={handleScheduleInterview}
      />

      <InterviewFeedbackDialog
        isOpen={isFeedbackDialogOpen}
        onClose={() => setIsFeedbackDialogOpen(false)}
        selectedInterview={selectedInterview}
        feedback={feedback}
        onFeedbackChange={handleFeedbackChange}
        onSubmitFeedback={handleSubmitFeedback}
      />

      <RescheduleInterviewDialog
        isOpen={isRescheduleDialogOpen}
        onClose={() => setIsRescheduleDialogOpen(false)}
        selectedInterview={selectedInterview}
        rescheduleData={rescheduleData}
        onRescheduleChange={handleRescheduleChange}
        onReschedule={handleRescheduleSubmit}
      />
    </div>
  );
}