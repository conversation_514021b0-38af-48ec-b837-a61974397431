import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { MemoryRouter } from 'react-router-dom'
import { TooltipProvider } from '@/components/ui/tooltip'
import { vi } from 'vitest'

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <MemoryRouter initialEntries={['/']}>
          {children}
        </MemoryRouter>
      </TooltipProvider>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Test data factories
export const createMockUser = (overrides = {}) => ({
  id: 'user-1',
  organizationId: 'org-1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'admin' as const,
  permissions: [],
  title: 'Test User',
  isActive: true,
  lastLogin: '2024-01-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  onboardingCompleted: true,
  ...overrides,
})

export const createMockCandidate = (overrides = {}) => ({
  id: 'candidate-1',
  firstName: 'Jane',
  lastName: 'Smith',
  email: '<EMAIL>',
  phone: '+**********',
  position: 'Software Engineer',
  department: 'Engineering',
  applicationDate: '2024-01-01',
  status: 'applied' as const,
  experience: 5,
  skills: ['React', 'TypeScript', 'Node.js'],
  ...overrides,
})

export const createMockJob = (overrides = {}) => ({
  id: 'job-1',
  title: 'Software Engineer',
  department: 'Engineering',
  location: 'Remote',
  type: 'full-time' as const,
  description: 'We are looking for a talented software engineer...',
  requirements: ['5+ years experience', 'React expertise', 'TypeScript knowledge'],
  postedDate: '2024-01-01',
  isActive: true,
  ...overrides,
})

// Mock API responses
export const mockApiResponse = <T,>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const mockApiError = (message = 'API Error', status = 500, delay = 0) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(message) as Error & { status: number }
      error.status = status
      reject(error)
    }, delay)
  })
}

// Mock intersection observer for components that use it
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn()
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.IntersectionObserver = mockIntersectionObserver
}

// Mock resize observer for components that use it
export const mockResizeObserver = () => {
  const mockResizeObserver = vi.fn()
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  })
  window.ResizeObserver = mockResizeObserver
}
