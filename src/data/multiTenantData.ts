import { Organization, User, MultiTenantJob, MultiTenantCandidate, Contract, VendorClientRelationship, Message, Notification } from '@/types/multiTenant';

export const mockOrganizations: Organization[] = [
  {
    id: 'org-1',
    name: 'TechCorp Solutions',
    type: 'client',
    subscriptionPlan: 'enterprise',
    industry: 'Technology',
    size: '1000+',
    website: 'https://techcorp.com',
    address: '123 Tech Street, San Francisco, CA 94105',
    phone: '+****************',
    email: '<EMAIL>',
    logo: 'https://images.unsplash.com/photo-1560179707-f14e90ef3623?w=100&h=100&fit=crop',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15',
    isActive: true,
    settings: {
      timezone: 'America/Los_Angeles',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
      allowPublicJobBoard: true,
      requireApprovalForJobs: true,
      maxUsers: 100,
      maxJobPostings: 50,
      customBranding: true,
    },
    clientProfile: {
      departments: [
        {
          id: 'dept-1',
          name: 'Engineering',
          description: 'Software Development and Infrastructure',
          managerId: 'user-2',
          budgetAllocated: 500000,
        },
        {
          id: 'dept-2',
          name: 'Product',
          description: 'Product Management and Strategy',
          managerId: 'user-3',
          budgetAllocated: 200000,
        },
      ],
      preferredVendors: ['org-2'],
      approvedVendors: ['org-2', 'org-3'],
      pendingVendorRequests: ['org-4'],
      hiringBudget: 2000000,
      annualHires: 150,
      companySize: '1000+',
      vendorSettings: {
        autoApproveVendors: false,
        requireVendorApproval: true,
        maxVendorsPerJob: 3,
        allowPublicJobAccess: false,
        vendorCommissionRate: 15,
      },
    },
  },
  {
    id: 'org-2',
    name: 'Elite Recruiters Inc',
    type: 'vendor',
    subscriptionPlan: 'professional',
    industry: 'Staffing & Recruiting',
    size: '51-200',
    website: 'https://eliterecruiters.com',
    address: '456 Recruiter Ave, New York, NY 10001',
    phone: '+****************',
    email: '<EMAIL>',
    logo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop',
    createdAt: '2024-01-02',
    updatedAt: '2024-01-16',
    isActive: true,
    settings: {
      timezone: 'America/New_York',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
      allowPublicJobBoard: false,
      requireApprovalForJobs: false,
      maxUsers: 25,
      maxJobPostings: 100,
      customBranding: false,
    },
    vendorProfile: {
      specializations: ['Technology', 'Finance', 'Healthcare'],
      certifications: ['RPR', 'CIR', 'CSP'],
      yearsInBusiness: 8,
      numberOfRecruiters: 45,
      successRate: 85,
      averageTimeToFill: 22,
      clientReviews: [
        {
          id: 'review-1',
          clientId: 'org-1',
          rating: 4,
          comment: 'Excellent service and quality candidates',
          createdAt: '2024-01-10',
          reviewer: 'John Smith, VP Engineering',
        },
      ],
      connectedClients: ['org-1'],
      pendingClientRequests: [],
      rejectedByClients: [],
      vendorTier: 'gold',
      minimumRate: 75,
      preferredIndustries: ['Technology', 'Finance', 'Healthcare'],
      serviceAreas: ['North America', 'Europe'],
    },
  },
  {
    id: 'org-3',
    name: 'Global Talent Partners',
    type: 'vendor',
    subscriptionPlan: 'basic',
    industry: 'Staffing & Recruiting',
    size: '11-50',
    website: 'https://globaltalent.com',
    address: '789 Talent Blvd, Austin, TX 73301',
    phone: '+****************',
    email: '<EMAIL>',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-17',
    isActive: true,
    settings: {
      timezone: 'America/Chicago',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
      allowPublicJobBoard: false,
      requireApprovalForJobs: false,
      maxUsers: 15,
      maxJobPostings: 25,
      customBranding: false,
    },
    vendorProfile: {
      specializations: ['Marketing', 'Sales', 'Operations'],
      certifications: ['RPR'],
      yearsInBusiness: 3,
      numberOfRecruiters: 12,
      successRate: 78,
      averageTimeToFill: 28,
      clientReviews: [],
      connectedClients: ['org-1'],
      pendingClientRequests: [],
      rejectedByClients: [],
      vendorTier: 'silver',
      minimumRate: 60,
      preferredIndustries: ['Technology', 'Retail', 'Manufacturing'],
      serviceAreas: ['North America'],
    },
  },
  {
    id: 'org-4',
    name: 'TechStaff Solutions',
    type: 'vendor',
    subscriptionPlan: 'basic',
    industry: 'Staffing & Recruiting',
    size: '11-50',
    website: 'https://techstaff.com',
    address: '321 Innovation Dr, Seattle, WA 98101',
    phone: '+****************',
    email: '<EMAIL>',
    createdAt: '2024-01-05',
    updatedAt: '2024-01-20',
    isActive: true,
    settings: {
      timezone: 'America/Los_Angeles',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
      allowPublicJobBoard: false,
      requireApprovalForJobs: false,
      maxUsers: 10,
      maxJobPostings: 20,
      customBranding: false,
    },
    vendorProfile: {
      specializations: ['Technology', 'Engineering', 'DevOps'],
      certifications: ['RPR', 'CIR'],
      yearsInBusiness: 5,
      numberOfRecruiters: 8,
      successRate: 82,
      averageTimeToFill: 25,
      clientReviews: [],
      connectedClients: [],
      pendingClientRequests: ['org-1'],
      rejectedByClients: [],
      vendorTier: 'bronze',
      minimumRate: 50,
      preferredIndustries: ['Technology', 'Startups'],
      serviceAreas: ['West Coast US'],
    },
  },
];

export const mockUsers: User[] = [
  {
    id: 'user-1',
    organizationId: 'org-1',
    email: '<EMAIL>',
    firstName: 'Alice',
    lastName: 'Johnson',
    role: 'admin',
    permissions: [
      { module: 'candidates', actions: ['create', 'read', 'update', 'delete'] },
      { module: 'jobs', actions: ['create', 'read', 'update', 'delete', 'approve'] },
      { module: 'interviews', actions: ['create', 'read', 'update', 'delete'] },
      { module: 'users', actions: ['create', 'read', 'update', 'delete'] },
      { module: 'settings', actions: ['read', 'update'] },
      { module: 'vendors', actions: ['read', 'update'] },
    ],
    avatar: '/images/professional.jpg',
    phone: '+****************',
    department: 'Human Resources',
    title: 'VP of Talent Acquisition',
    isActive: true,
    lastLogin: '2024-01-25T10:30:00Z',
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-25T10:30:00Z',
    onboardingCompleted: true,
  },
  {
    id: 'user-2',
    organizationId: 'org-1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Smith',
    role: 'hiring_manager',
    permissions: [
      { module: 'candidates', actions: ['read', 'update'] },
      { module: 'jobs', actions: ['create', 'read', 'update'] },
      { module: 'interviews', actions: ['create', 'read', 'update'] },
    ],
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    phone: '+****************',
    department: 'Engineering',
    title: 'Engineering Manager',
    isActive: true,
    lastLogin: '2024-01-24T15:45:00Z',
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-24T15:45:00Z',
    onboardingCompleted: true,
  },
  {
    id: 'user-3',
    organizationId: 'org-2',
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Wilson',
    role: 'admin',
    permissions: [
      { module: 'candidates', actions: ['create', 'read', 'update', 'delete'] },
      { module: 'jobs', actions: ['read', 'update'] },
      { module: 'interviews', actions: ['create', 'read', 'update'] },
      { module: 'users', actions: ['create', 'read', 'update', 'delete'] },
    ],
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    phone: '+****************',
    department: 'Operations',
    title: 'Managing Director',
    isActive: true,
    lastLogin: '2024-01-25T08:20:00Z',
    createdAt: '2024-01-02T10:00:00Z',
    updatedAt: '2024-01-25T08:20:00Z',
    onboardingCompleted: true,
  },
  {
    id: 'user-4',
    organizationId: 'org-2',
    email: '<EMAIL>',
    firstName: 'Mike',
    lastName: 'Davis',
    role: 'recruiter',
    permissions: [
      { module: 'candidates', actions: ['create', 'read', 'update'] },
      { module: 'jobs', actions: ['read'] },
      { module: 'interviews', actions: ['create', 'read', 'update'] },
    ],
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    phone: '+****************',
    department: 'Recruiting',
    title: 'Senior Recruiter',
    isActive: true,
    lastLogin: '2024-01-25T11:15:00Z',
    createdAt: '2024-01-02T10:00:00Z',
    updatedAt: '2024-01-25T11:15:00Z',
    onboardingCompleted: true,
  },
];

export const mockMultiTenantJobs: MultiTenantJob[] = [
  {
    id: 'job-mt-1',
    title: 'Senior Full Stack Developer',
    department: 'Engineering',
    location: 'San Francisco, CA (Remote)',
    type: 'full-time',
    description: 'We are seeking a senior full stack developer to join our engineering team and work on cutting-edge applications.',
    requirements: ['5+ years React/Node.js', 'TypeScript proficiency', 'AWS experience', 'Team leadership'],
    postedDate: '2024-01-15',
    isActive: true,
    organizationId: 'org-1',
    createdBy: 'user-1',
    assignedVendors: ['org-2'],
    clientDepartment: 'Engineering',
    budget: 180000,
    priority: 'high',
    isPublic: true,
    jobCode: 'ENG-001-2024',
    approvalStatus: 'approved',
    approvedBy: 'user-1',
    approvedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 'job-mt-2',
    title: 'Product Manager',
    department: 'Product',
    location: 'New York, NY',
    type: 'full-time',
    description: 'Looking for an experienced product manager to drive our product strategy and roadmap.',
    requirements: ['5+ years product management', 'B2B SaaS experience', 'Analytics skills', 'Stakeholder management'],
    postedDate: '2024-01-18',
    isActive: true,
    organizationId: 'org-1',
    createdBy: 'user-2',
    assignedVendors: ['org-2', 'org-3'],
    clientDepartment: 'Product',
    budget: 160000,
    priority: 'medium',
    isPublic: false,
    jobCode: 'PRD-002-2024',
    approvalStatus: 'approved',
    approvedBy: 'user-1',
    approvedAt: '2024-01-18T14:30:00Z',
  },
];

export const mockMultiTenantCandidates: MultiTenantCandidate[] = [
  {
    id: 'cand-mt-1',
    firstName: 'Emma',
    lastName: 'Thompson',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Senior Full Stack Developer',
    department: 'Engineering',
    applicationDate: '2024-01-20',
    status: 'interview',
    experience: 6,
    skills: ['React', 'Node.js', 'TypeScript', 'AWS', 'Docker'],
    avatar: '/images/professional.jpg',
    organizationId: 'org-2',
    sourceOrganizationId: 'org-2',
    submittedBy: 'user-4',
    jobId: 'job-mt-1',
    vendorId: 'org-2',
    availability: 'Immediately',
    noticePeriod: '2 weeks',
    currentEmployer: 'StartupXYZ',
    referralSource: 'LinkedIn',
    rateCard: {
      annualSalary: 175000,
      currency: 'USD',
      negotiable: true,
    },
    clientFeedback: [
      {
        id: 'feedback-1',
        userId: 'user-2',
        userName: 'John Smith',
        feedback: 'Strong technical skills, good cultural fit',
        rating: 4,
        createdAt: '2024-01-22T16:00:00Z',
        stage: 'technical_interview',
      },
    ],
    vendorNotes: 'Excellent candidate with strong React skills. Previously worked at Google.',
  },
  {
    id: 'cand-mt-2',
    firstName: 'David',
    lastName: 'Chen',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Product Manager',
    department: 'Product',
    applicationDate: '2024-01-22',
    status: 'screening',
    experience: 7,
    skills: ['Product Strategy', 'Agile', 'Data Analysis', 'Stakeholder Management'],
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    organizationId: 'org-3',
    sourceOrganizationId: 'org-3',
    submittedBy: 'user-3',
    jobId: 'job-mt-2',
    vendorId: 'org-3',
    availability: '1 month',
    noticePeriod: '4 weeks',
    currentEmployer: 'ProductCorp',
    referralSource: 'Referral',
    rateCard: {
      annualSalary: 155000,
      currency: 'USD',
      negotiable: false,
    },
    vendorNotes: 'Strong product background with experience in B2B SaaS.',
  },
];

export const mockContracts: Contract[] = [
  {
    id: 'contract-1',
    clientId: 'org-1',
    vendorId: 'org-2',
    type: 'master_service',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    value: 500000,
    currency: 'USD',
    status: 'active',
    terms: {
      paymentTerms: 'Net 30',
      deliverables: ['Candidate sourcing', 'Interview coordination', 'Background checks'],
      penalties: '5% of placement fee for early termination',
      cancellationPolicy: '30 days notice required',
      markupPercentage: 25,
    },
    createdBy: 'user-1',
    createdAt: '2024-01-01T09:00:00Z',
  },
];

// Vendor-Client Relationships
export const mockVendorClientRelationships: VendorClientRelationship[] = [
  {
    id: 'rel-1',
    vendorId: 'org-2',
    clientId: 'org-1',
    status: 'approved',
    requestedBy: 'client',
    requestedAt: '2024-01-01T09:00:00Z',
    approvedAt: '2024-01-01T10:00:00Z',
    approvedBy: 'user-1',
    contractTerms: {
      commissionRate: 15,
      paymentTerms: 'Net 30',
      guaranteePeriod: 90,
      cancellationNotice: 30,
    },
    performanceMetrics: {
      totalSubmissions: 25,
      totalHires: 8,
      averageTimeToSubmit: 3,
      averageTimeToHire: 18,
      clientSatisfactionRating: 4.2,
      lastUpdated: '2024-01-25T00:00:00Z',
    },
  },
  {
    id: 'rel-2',
    vendorId: 'org-3',
    clientId: 'org-1',
    status: 'approved',
    requestedBy: 'vendor',
    requestedAt: '2024-01-02T09:00:00Z',
    approvedAt: '2024-01-03T14:00:00Z',
    approvedBy: 'user-1',
    contractTerms: {
      commissionRate: 18,
      paymentTerms: 'Net 45',
      guaranteePeriod: 60,
      cancellationNotice: 14,
    },
    performanceMetrics: {
      totalSubmissions: 12,
      totalHires: 3,
      averageTimeToSubmit: 5,
      averageTimeToHire: 22,
      clientSatisfactionRating: 3.8,
      lastUpdated: '2024-01-25T00:00:00Z',
    },
  },
  {
    id: 'rel-3',
    vendorId: 'org-4',
    clientId: 'org-1',
    status: 'pending',
    requestedBy: 'vendor',
    requestedAt: '2024-01-20T09:00:00Z',
  },
];

// Sample Messages
export const mockMessages: Message[] = [
  {
    id: 'msg-1',
    fromOrganizationId: 'org-2',
    toOrganizationId: 'org-1',
    fromUserId: 'user-3',
    toUserId: 'user-1',
    subject: 'New Candidate Submission for Senior Frontend Developer',
    content: 'Hi Alice, I have submitted a strong candidate for the Senior Frontend Developer position. Please review when you have a chance.',
    messageType: 'candidate-related',
    relatedJobId: 'job-1',
    relatedCandidateId: 'candidate-1',
    isRead: false,
    createdAt: '2024-01-25T14:30:00Z',
    attachments: [],
  },
  {
    id: 'msg-2',
    fromOrganizationId: 'org-1',
    toOrganizationId: 'org-2',
    fromUserId: 'user-1',
    subject: 'New Job Assignment: DevOps Engineer',
    content: 'We have a new urgent requirement for a DevOps Engineer. Please find qualified candidates ASAP.',
    messageType: 'job-related',
    relatedJobId: 'job-2',
    isRead: true,
    createdAt: '2024-01-24T09:15:00Z',
    readAt: '2024-01-24T09:45:00Z',
    attachments: [],
  },
];

// Sample Notifications
export const mockNotifications: Notification[] = [
  {
    id: 'notif-1',
    userId: 'user-1',
    organizationId: 'org-1',
    type: 'candidate_submitted',
    title: 'New Candidate Submitted',
    message: 'Elite Recruiters Inc has submitted a candidate for Senior Frontend Developer position',
    isRead: false,
    createdAt: '2024-01-25T14:30:00Z',
    actionUrl: '/candidates/candidate-1',
    relatedJobId: 'job-1',
    relatedCandidateId: 'candidate-1',
  },
  {
    id: 'notif-2',
    userId: 'user-3',
    organizationId: 'org-2',
    type: 'job_assigned',
    title: 'New Job Assignment',
    message: 'You have been assigned to work on DevOps Engineer position',
    isRead: true,
    createdAt: '2024-01-24T09:15:00Z',
    readAt: '2024-01-24T09:45:00Z',
    actionUrl: '/jobs/job-2',
    relatedJobId: 'job-2',
  },
];