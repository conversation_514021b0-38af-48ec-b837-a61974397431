import { Candidate, Job, OnboardingTask, Interview } from '@/types';

export const mockCandidates: Candidate[] = [
  {
    id: '1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Senior Frontend Developer',
    department: 'Engineering',
    applicationDate: '2024-01-15',
    status: 'onboarding',
    experience: 5,
    skills: ['React', 'TypeScript', 'Node.js', 'AWS'],
    onboardingStage: 'training',
    startDate: '2024-02-01',
    avatar: '/images/avatar.jpg'
  },
  {
    id: '2',
    firstName: '<PERSON>',
    lastName: 'Chen',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Product Manager',
    department: 'Product',
    applicationDate: '2024-01-20',
    status: 'offer',
    experience: 7,
    skills: ['Product Strategy', 'Agile', 'Analytics', 'Leadership'],
    interviewDate: '2024-01-25',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '3',
    firstName: 'Emily',
    lastName: 'Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'UX Designer',
    department: 'Design',
    applicationDate: '2024-01-18',
    status: 'interview',
    experience: 3,
    skills: ['Figma', 'User Research', 'Prototyping', 'Design Systems'],
    interviewDate: '2024-01-28',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '4',
    firstName: 'David',
    lastName: 'Thompson',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Data Scientist',
    department: 'Analytics',
    applicationDate: '2024-01-22',
    status: 'screening',
    experience: 4,
    skills: ['Python', 'Machine Learning', 'SQL', 'Statistics'],
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  }
];

export const mockJobs: Job[] = [
  {
    id: '1',
    title: 'Senior Frontend Developer',
    department: 'Engineering',
    location: 'San Francisco, CA',
    type: 'full-time',
    description: 'We are looking for a Senior Frontend Developer to join our engineering team.',
    requirements: ['5+ years React experience', 'TypeScript proficiency', 'Team leadership'],
    postedDate: '2024-01-10',
    isActive: true
  },
  {
    id: '2',
    title: 'Product Manager',
    department: 'Product',
    location: 'New York, NY',
    type: 'full-time',
    description: 'Join our product team to drive strategic initiatives and product roadmap.',
    requirements: ['5+ years product management', 'B2B SaaS experience', 'Analytics skills'],
    postedDate: '2024-01-12',
    isActive: true
  },
  {
    id: '3',
    title: 'UX Designer',
    department: 'Design',
    location: 'Remote',
    type: 'full-time',
    description: 'Create exceptional user experiences for our digital products.',
    requirements: ['3+ years UX design', 'Figma expertise', 'User research experience'],
    postedDate: '2024-01-14',
    isActive: true
  }
];

export const mockOnboardingTasks: OnboardingTask[] = [
  {
    id: '1',
    candidateId: '1',
    title: 'Complete I-9 Form',
    description: 'Fill out and submit the Employment Eligibility Verification form',
    dueDate: '2024-02-03',
    status: 'completed',
    assignedTo: 'HR Team',
    category: 'documentation'
  },
  {
    id: '2',
    candidateId: '1',
    title: 'Security Training',
    description: 'Complete mandatory cybersecurity training modules',
    dueDate: '2024-02-05',
    status: 'in-progress',
    assignedTo: 'Security Team',
    category: 'training'
  },
  {
    id: '3',
    candidateId: '1',
    title: 'Setup Development Environment',
    description: 'Configure laptop and development tools',
    dueDate: '2024-02-02',
    status: 'completed',
    assignedTo: 'IT Team',
    category: 'setup'
  },
  {
    id: '4',
    candidateId: '1',
    title: 'Team Introduction Meeting',
    description: 'Meet with team members and understand project scope',
    dueDate: '2024-02-06',
    status: 'pending',
    assignedTo: 'Engineering Manager',
    category: 'orientation'
  }
];

export const mockInterviews: Interview[] = [
  {
    id: '1',
    candidateId: '3',
    date: '2024-01-28',
    time: '10:00 AM',
    interviewer: 'Jane Smith',
    type: 'video',
    status: 'scheduled'
  },
  {
    id: '2',
    candidateId: '2',
    date: '2024-01-25',
    time: '2:00 PM',
    interviewer: 'John Doe',
    type: 'in-person',
    status: 'completed',
    feedback: 'Strong candidate with excellent product sense',
    rating: 4
  }
];