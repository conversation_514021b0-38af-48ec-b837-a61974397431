import { Message, Conversation, Notification, ActivityFeedItem, OnlineStatus } from '@/types/communication';

// Mock Messages
export const mockMessages: Message[] = [
  {
    id: 'msg-1',
    conversationId: 'conv-1',
    senderId: 'user-2',
    senderName: '<PERSON>',
    senderOrganization: 'Elite Recruiters Inc',
    recipientId: 'user-1',
    recipientName: '<PERSON>',
    recipientOrganization: 'TechCorp Solutions',
    content: 'Hi <PERSON>! I have a great candidate for the Senior React Developer position. Would you like me to submit their profile?',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    isRead: false,
    messageType: 'text',
    relatedJobId: 'job-1'
  },
  {
    id: 'msg-2',
    conversationId: 'conv-1',
    senderId: 'user-1',
    senderName: '<PERSON>',
    senderOrganization: 'TechCorp Solutions',
    recipientId: 'user-2',
    recipientName: '<PERSON>',
    recipientOrganization: 'Elite Recruiters Inc',
    content: 'Yes, please go ahead and submit the candidate. Make sure to include their portfolio and recent project details.',
    timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
    isRead: true,
    messageType: 'text',
    relatedJobId: 'job-1'
  },
  {
    id: 'msg-3',
    conversationId: 'conv-1',
    senderId: 'user-2',
    senderName: 'Sarah Wilson',
    senderOrganization: 'Elite Recruiters Inc',
    recipientId: 'user-1',
    recipientName: 'Alice Johnson',
    recipientOrganization: 'TechCorp Solutions',
    content: 'Perfect! I\'ve submitted John Smith for the position. He has 5+ years of React experience and has worked on several large-scale applications.',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    isRead: false,
    messageType: 'text',
    relatedJobId: 'job-1',
    relatedCandidateId: 'candidate-1'
  }
];

// Mock Conversations
export const mockConversations: Conversation[] = [
  {
    id: 'conv-1',
    participants: [
      {
        userId: 'user-1',
        userName: 'Alice Johnson',
        organizationId: 'org-1',
        organizationName: 'TechCorp Solutions',
        role: 'client',
        avatar: '/avatars/alice.jpg',
        isOnline: true
      },
      {
        userId: 'user-2',
        userName: 'Sarah Wilson',
        organizationId: 'org-2',
        organizationName: 'Elite Recruiters Inc',
        role: 'vendor',
        avatar: '/avatars/sarah.jpg',
        isOnline: false,
        lastSeen: new Date(Date.now() - 15 * 60 * 1000).toISOString()
      }
    ],
    lastMessage: mockMessages[2],
    lastActivity: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    isActive: true,
    subject: 'Senior React Developer Position',
    relatedJobId: 'job-1',
    unreadCount: 2
  },
  {
    id: 'conv-2',
    participants: [
      {
        userId: 'user-1',
        userName: 'Alice Johnson',
        organizationId: 'org-1',
        organizationName: 'TechCorp Solutions',
        role: 'client',
        avatar: '/avatars/alice.jpg',
        isOnline: true
      },
      {
        userId: 'user-4',
        userName: 'Bob Johnson',
        organizationId: 'org-3',
        organizationName: 'Global Talent Solutions',
        role: 'vendor',
        avatar: '/avatars/bob.jpg',
        isOnline: true
      }
    ],
    lastMessage: {
      id: 'msg-4',
      conversationId: 'conv-2',
      senderId: 'user-4',
      senderName: 'Bob Johnson',
      senderOrganization: 'Global Talent Solutions',
      recipientId: 'user-1',
      recipientName: 'Alice Johnson',
      recipientOrganization: 'TechCorp Solutions',
      content: 'Thanks for the feedback on the candidates. I\'ll focus on finding more senior-level developers.',
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      isRead: true,
      messageType: 'text'
    },
    lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: true,
    subject: 'General Discussion',
    unreadCount: 0
  }
];

// Mock Notifications
export const mockNotifications: Notification[] = [
  {
    id: 'notif-1',
    userId: 'user-1',
    type: 'candidate_submitted',
    title: 'New Candidate Submitted',
    message: 'Sarah Wilson submitted John Smith for Senior React Developer position',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    isRead: false,
    actionUrl: '/vendor-submissions',
    relatedJobId: 'job-1',
    relatedCandidateId: 'candidate-1',
    relatedUserId: 'user-2',
    priority: 'high',
    category: 'candidate'
  },
  {
    id: 'notif-2',
    userId: 'user-1',
    type: 'message_received',
    title: 'New Message',
    message: 'Sarah Wilson sent you a message about Senior React Developer position',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    isRead: false,
    actionUrl: '/messages/conv-1',
    relatedUserId: 'user-2',
    priority: 'medium',
    category: 'message'
  },
  {
    id: 'notif-3',
    userId: 'user-2',
    type: 'job_assigned',
    title: 'New Job Assignment',
    message: 'You have been assigned to work on Senior React Developer position',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    isRead: true,
    actionUrl: '/vendor-jobs',
    relatedJobId: 'job-1',
    relatedUserId: 'user-1',
    priority: 'high',
    category: 'job'
  },
  {
    id: 'notif-4',
    userId: 'user-2',
    type: 'candidate_approved',
    title: 'Candidate Approved',
    message: 'Alice Johnson approved your candidate John Smith for interview',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    isRead: true,
    actionUrl: '/vendor-jobs',
    relatedJobId: 'job-1',
    relatedCandidateId: 'candidate-1',
    relatedUserId: 'user-1',
    priority: 'high',
    category: 'candidate'
  }
];

// Mock Activity Feed
export const mockActivityFeed: ActivityFeedItem[] = [
  {
    id: 'activity-1',
    userId: 'user-1',
    type: 'candidate_submitted',
    title: 'Candidate Submitted',
    description: 'Sarah Wilson submitted John Smith for Senior React Developer',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    actorId: 'user-2',
    actorName: 'Sarah Wilson',
    actorOrganization: 'Elite Recruiters Inc',
    targetName: 'John Smith',
    relatedJobId: 'job-1',
    relatedCandidateId: 'candidate-1',
    icon: 'UserPlus',
    color: 'text-blue-600'
  },
  {
    id: 'activity-2',
    userId: 'user-1',
    type: 'job_assigned',
    title: 'Job Assigned',
    description: 'Assigned Senior React Developer position to Elite Recruiters Inc',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    actorId: 'user-1',
    actorName: 'Alice Johnson',
    actorOrganization: 'TechCorp Solutions',
    targetName: 'Elite Recruiters Inc',
    relatedJobId: 'job-1',
    icon: 'Briefcase',
    color: 'text-green-600'
  },
  {
    id: 'activity-3',
    userId: 'user-2',
    type: 'candidate_approved',
    title: 'Candidate Approved',
    description: 'Alice Johnson approved John Smith for interview',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    actorId: 'user-1',
    actorName: 'Alice Johnson',
    actorOrganization: 'TechCorp Solutions',
    targetName: 'John Smith',
    relatedJobId: 'job-1',
    relatedCandidateId: 'candidate-1',
    icon: 'CheckCircle',
    color: 'text-green-600'
  },
  {
    id: 'activity-4',
    userId: 'user-1',
    type: 'vendor_connected',
    title: 'Vendor Connected',
    description: 'Connected with Global Talent Solutions',
    timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    actorId: 'user-1',
    actorName: 'Alice Johnson',
    actorOrganization: 'TechCorp Solutions',
    targetName: 'Global Talent Solutions',
    icon: 'Handshake',
    color: 'text-purple-600'
  }
];

// Mock Online Status
export const mockOnlineStatus: OnlineStatus[] = [
  {
    userId: 'user-1',
    isOnline: true,
    lastSeen: new Date().toISOString(),
    status: 'online'
  },
  {
    userId: 'user-2',
    isOnline: false,
    lastSeen: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    status: 'away'
  },
  {
    userId: 'user-3',
    isOnline: false,
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    status: 'offline'
  },
  {
    userId: 'user-4',
    isOnline: true,
    lastSeen: new Date().toISOString(),
    status: 'online'
  }
];
