import React, { Suspense, useEffect } from 'react';
import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import ErrorBoundary from './components/ErrorBoundary';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';
import { PerformanceMonitor } from './lib/performance';

// Lazy load heavy components for better performance
const Candidates = React.lazy(() => import('./pages/Candidates'));
const Jobs = React.lazy(() => import('./pages/Jobs'));
const VendorJobs = React.lazy(() => import('./pages/VendorJobs'));
const CandidateSubmission = React.lazy(() => import('./pages/CandidateSubmission'));
const VendorSubmissions = React.lazy(() => import('./pages/VendorSubmissions'));
const Messages = React.lazy(() => import('./pages/Messages'));
const Analytics = React.lazy(() => import('./pages/Analytics'));
const Interviews = React.lazy(() => import('./pages/Interviews'));
const Onboarding = React.lazy(() => import('./pages/Onboarding'));
const VendorManagement = React.lazy(() => import('./pages/VendorManagement'));
const Settings = React.lazy(() => import('./pages/Settings'));
const NotFound = React.lazy(() => import('./pages/NotFound'));

// Loading component for Suspense fallback
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span className="ml-3 text-gray-600">Loading...</span>
  </div>
);

const queryClient = new QueryClient();

const App = () => {
  // Initialize performance monitoring
  useEffect(() => {
    PerformanceMonitor.init();

    // Cleanup on unmount
    return () => {
      PerformanceMonitor.cleanup();
    };
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <Toaster />
            <BrowserRouter>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />

                {/* Protected routes */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }>
                  <Route index element={<Dashboard />} />
                  <Route path="candidates" element={
                    <Suspense fallback={<PageLoader />}>
                      <Candidates />
                    </Suspense>
                  } />
                  <Route path="jobs" element={
                    <Suspense fallback={<PageLoader />}>
                      <Jobs />
                    </Suspense>
                  } />
                  <Route path="vendor-jobs" element={
                    <Suspense fallback={<PageLoader />}>
                      <VendorJobs />
                    </Suspense>
                  } />
                  <Route path="submit-candidate/:jobId" element={
                    <Suspense fallback={<PageLoader />}>
                      <CandidateSubmission />
                    </Suspense>
                  } />
                  <Route path="vendor-submissions" element={
                    <Suspense fallback={<PageLoader />}>
                      <VendorSubmissions />
                    </Suspense>
                  } />
                  <Route path="messages" element={
                    <Suspense fallback={<PageLoader />}>
                      <Messages />
                    </Suspense>
                  } />
                  <Route path="analytics" element={
                    <Suspense fallback={<PageLoader />}>
                      <Analytics />
                    </Suspense>
                  } />
                  <Route path="interviews" element={
                    <Suspense fallback={<PageLoader />}>
                      <Interviews />
                    </Suspense>
                  } />
                  <Route path="onboarding" element={
                    <Suspense fallback={<PageLoader />}>
                      <Onboarding />
                    </Suspense>
                  } />
                  <Route path="vendors" element={
                    <Suspense fallback={<PageLoader />}>
                      <VendorManagement />
                    </Suspense>
                  } />
                  <Route path="settings" element={
                    <Suspense fallback={<PageLoader />}>
                      <Settings />
                    </Suspense>
                  } />
                </Route>

                {/* 404 route */}
                <Route path="*" element={
                  <Suspense fallback={<PageLoader />}>
                    <NotFound />
                  </Suspense>
                } />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
