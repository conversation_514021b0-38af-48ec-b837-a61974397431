# Project Summary
The TalentFlow ATS Platform is a comprehensive web application designed to streamline the onboarding journeys for HR and Talent Acquisition teams. It manages the entire candidate journey from application through onboarding, featuring a candidate application portal, HR dashboard, interview scheduling system, and onboarding workflow management. The platform aims to enhance recruitment efficiency and improve candidate experience.

# Project Module Description
- **Dashboard**: Provides an overview of recruitment metrics, including candidate status and onboarding progress.
- **Candidates Management**: Allows HR to manage candidate profiles, track their progress, and filter candidates based on various criteria.
- **Job Postings**: Enables job creation, management, and applicant tracking for different positions.
- **Interview Management**: Facilitates scheduling and managing interviews with candidates.
- **Onboarding Workflows**: Manages the onboarding process through various stages, ensuring new hires complete necessary tasks.
- **Settings & Configuration**: Allows customization of the application, including company information and notification preferences.

# Directory Tree
```
shadcn-ui/
├── README.md                # Project overview and setup instructions
├── components.json          # Component definitions
├── eslint.config.js         # ESLint configuration
├── index.html               # Main HTML file
├── package.json             # Project dependencies and scripts
├── postcss.config.js        # PostCSS configuration
├── public/                  # Public assets
│   ├── favicon.svg          # Favicon for the application
│   ├── images/              # Image assets
│   └── robots.txt           # Robots.txt file
├── src/                     # Source files
│   ├── App.css              # Main application styles
│   ├── App.tsx              # Main application component
│   ├── components/          # UI components
│   ├── data/                # Mock data for testing
│   ├── hooks/               # Custom hooks
│   ├── pages/               # Application pages
│   ├── types/               # TypeScript type definitions
│   ├── vite-env.d.ts        # Vite environment types
│   └── vite.config.ts       # Vite configuration
└── tailwind.config.ts       # Tailwind CSS configuration
```

# File Description Inventory
- **README.md**: Contains project details and setup instructions.
- **package.json**: Lists project dependencies and scripts for building and running the application.
- **src/App.tsx**: The main component that initializes the application and now includes necessary imports.
- **src/pages/**: Contains individual page components for the application (e.g., Dashboard, Candidates, Jobs).
- **src/components/**: Contains reusable UI components (e.g., buttons, cards, forms).
- **src/data/mockData.ts**: Contains mock data for candidates, jobs, and onboarding tasks.
- **src/types/index.ts**: TypeScript interfaces for defining data structures used in the application.

# Technology Stack
- **Frontend**: React, TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React hooks
- **Routing**: React Router

# Usage
To get started with the TalentFlow ATS Platform:
1. Install dependencies:
   ```bash
   pnpm install
   ```
2. Run linting to ensure code quality:
   ```bash
   pnpm run lint
   ```
3. Start the development server:
   ```bash
   pnpm run dev
   ```
